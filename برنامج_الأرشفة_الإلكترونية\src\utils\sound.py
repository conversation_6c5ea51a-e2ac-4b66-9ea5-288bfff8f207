import os
from PyQt5.QtCore import QUrl
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

class SoundManager:
    """مدير الأصوات والمؤثرات الصوتية في البرنامج"""
    
    def __init__(self):
        """تهيئة مدير الأصوات"""
        self.sounds_enabled = True
        self.sound_volume = 50  # نسبة مئوية
        
        # إنشاء مشغل الوسائط
        self.player = QMediaPlayer()
        self.player.setVolume(self.sound_volume)
        
        # تحديد مسار ملفات الصوت
        self.sounds_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'resources', 'sounds')
        
        # إنشاء مجلد الأصوات إذا لم يكن موجوداً
        if not os.path.exists(self.sounds_dir):
            os.makedirs(self.sounds_dir)
        
        # تعريف الأصوات الافتراضية
        self.sounds = {
            'click': 'click.wav',
            'hover': 'hover.wav',
            'success': 'success.wav',
            'error': 'error.wav',
            'notification': 'notification.wav'
        }
        
        # إنشاء ملفات الصوت الافتراضية إذا لم تكن موجودة
        self.create_default_sounds()
    
    def create_default_sounds(self):
        """إنشاء ملفات الصوت الافتراضية"""
        # هذه الوظيفة ستكون فارغة في الوقت الحالي
        # في التطبيق الفعلي، يمكن تضمين ملفات الصوت مع البرنامج
        pass
    
    def enable_sounds(self, enabled=True):
        """تفعيل أو تعطيل الأصوات"""
        self.sounds_enabled = enabled
    
    def set_volume(self, volume):
        """تعيين مستوى الصوت (0-100)"""
        self.sound_volume = max(0, min(100, volume))
        self.player.setVolume(self.sound_volume)
    
    def play_sound(self, sound_name):
        """تشغيل صوت محدد"""
        if not self.sounds_enabled or sound_name not in self.sounds:
            return
        
        sound_file = os.path.join(self.sounds_dir, self.sounds[sound_name])
        
        # تجاهل تشغيل الصوت إذا لم يكن الملف موجوداً
        if not os.path.exists(sound_file):
            return
        
        self.player.setMedia(QMediaContent(QUrl.fromLocalFile(sound_file)))
        self.player.play()
    
    def play_click_sound(self):
        """تشغيل صوت النقر"""
        self.play_sound('click')
    
    def play_hover_sound(self):
        """تشغيل صوت التحويم"""
        self.play_sound('hover')
    
    def play_success_sound(self):
        """تشغيل صوت النجاح"""
        self.play_sound('success')
    
    def play_error_sound(self):
        """تشغيل صوت الخطأ"""
        self.play_sound('error')
    
    def play_notification_sound(self):
        """تشغيل صوت الإشعار"""
        self.play_sound('notification')
