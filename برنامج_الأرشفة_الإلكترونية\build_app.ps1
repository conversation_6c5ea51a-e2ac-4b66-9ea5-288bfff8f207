# PowerShell script to build the Electronic Archiving Program
# سكريبت PowerShell لبناء برنامج الأرشفة الإلكترونية

Write-Host "================================================" -ForegroundColor Cyan
Write-Host "🏗️  بناء تطبيق الأرشفة الإلكترونية" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من Python
Write-Host "🔧 التحقق من Python..." -ForegroundColor Green
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""

# تثبيت PyInstaller
Write-Host "📦 تثبيت PyInstaller..." -ForegroundColor Green
try {
    pip install pyinstaller
    Write-Host "✅ تم تثبيت PyInstaller بنجاح" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في تثبيت PyInstaller" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""

# إنشاء الملف التنفيذي
Write-Host "🚀 إنشاء الملف التنفيذي..." -ForegroundColor Green
try {
    pyinstaller --onefile --windowed --name="برنامج_الأرشفة_الإلكترونية" src/__main__.py
    Write-Host "✅ تم إنشاء الملف التنفيذي بنجاح!" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في إنشاء الملف التنفيذي" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "🎉 تم الانتهاء بنجاح!" -ForegroundColor Yellow
Write-Host "📁 تحقق من مجلد dist للملف التنفيذي" -ForegroundColor Cyan
Write-Host ""

# فتح مجلد dist إذا كان موجوداً
if (Test-Path "dist") {
    Write-Host "🔍 فتح مجلد المخرجات..." -ForegroundColor Green
    Start-Process "dist"
}

Read-Host "اضغط Enter للخروج"
