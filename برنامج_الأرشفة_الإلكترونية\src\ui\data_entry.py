import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QDateEdit, QTextEdit, QPushButton, QFormLayout,
                           QMessageBox, QGraphicsDropShadowEffect, QCalendarWidget)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDate, QSize, QPropertyAnimation, QEasingCurve

from ..utils.themes import ThemeManager
from ..utils.sound import SoundManager
from ..database.db_manager import DatabaseManager

class DataEntryScreen(QMainWindow):
    """واجهة إدخال البيانات الجديدة"""
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        self.sound_manager = SoundManager()
        self.db_manager = db_manager
        
        self.setWindowTitle("إدخال بيانات جديدة - برنامج الأرشفة الإلكترونية")
        self.setMinimumSize(800, 600)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد الخلفية والأنماط
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.theme_manager.get_color('background')};
            }}
            QLabel {{
                color: {self.theme_manager.get_color('text')};
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: 500;
            }}
            QLabel#header {{
                color: {self.theme_manager.get_color('primary')};
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 28px;
                font-weight: bold;
                padding: 15px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
                letter-spacing: 1px;
            }}
            QLineEdit, QTextEdit, QDateEdit {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }}
            QLineEdit:focus, QTextEdit:focus, QDateEdit:focus {{
                border: 2px solid {self.theme_manager.get_color('primary')};
            }}
            QPushButton {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 12px 24px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 16px;
                font-weight: bold;
                min-height: 40px;
                letter-spacing: 0.5px;
            }}
            QPushButton:hover {{
                background-color: {self.theme_manager.get_color('primary_light')};
                border: 1px solid rgba(255, 255, 255, 0.4);
                transform: translateY(-1px);
            }}
            QPushButton#cancel {{
                background-color: {self.theme_manager.get_color('error')};
            }}
            QPushButton#cancel:hover {{
                background-color: #ff5252;
            }}
        """)
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        header_label = QLabel("إدخال بيانات جديدة")
        header_label.setObjectName("header")
        header_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header_label)
        
        # نموذج إدخال البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFormAlignment(Qt.AlignRight)
        form_layout.setSpacing(15)
        
        # رقم الكتاب
        self.doc_number_input = QLineEdit()
        self.doc_number_input.setPlaceholderText("أدخل رقم الكتاب")
        self.add_shadow_effect(self.doc_number_input)
        form_layout.addRow("رقم الكتاب:", self.doc_number_input)
        
        # تاريخ الكتاب
        date_layout = QHBoxLayout()
        date_layout.setSpacing(10)
        
        # اليوم
        self.day_input = QLineEdit()
        self.day_input.setPlaceholderText("اليوم")
        self.day_input.setMaximumWidth(80)
        self.add_shadow_effect(self.day_input)
        date_layout.addWidget(self.day_input)
        
        # الشهر
        self.month_input = QLineEdit()
        self.month_input.setPlaceholderText("الشهر")
        self.month_input.setMaximumWidth(80)
        self.add_shadow_effect(self.month_input)
        date_layout.addWidget(self.month_input)
        
        # السنة
        self.year_input = QLineEdit()
        self.year_input.setPlaceholderText("السنة")
        self.year_input.setMaximumWidth(100)
        self.add_shadow_effect(self.year_input)
        date_layout.addWidget(self.year_input)
        
        # زر التقويم
        calendar_button = QPushButton()
        calendar_button.setIcon(QIcon.fromTheme("office-calendar"))
        calendar_button.setIconSize(QSize(20, 20))
        calendar_button.setMaximumWidth(40)
        calendar_button.clicked.connect(self.show_calendar)
        date_layout.addWidget(calendar_button)
        
        date_layout.addStretch()
        form_layout.addRow("تاريخ الكتاب:", date_layout)
        
        # الموضوع
        self.subject_input = QLineEdit()
        self.subject_input.setPlaceholderText("أدخل موضوع الكتاب")
        self.add_shadow_effect(self.subject_input)
        form_layout.addRow("الموضوع:", self.subject_input)
        
        # الفحوى أو اسم الموظف المعني
        self.content_input = QTextEdit()
        self.content_input.setPlaceholderText("أدخل الفحوى أو اسم الموظف المعني")
        self.content_input.setMinimumHeight(100)
        self.add_shadow_effect(self.content_input)
        form_layout.addRow("الفحوى أو اسم الموظف المعني:", self.content_input)
        
        # الدائرة المعنية
        self.department_input = QLineEdit()
        self.department_input.setPlaceholderText("أدخل اسم الدائرة المعنية")
        self.add_shadow_effect(self.department_input)
        form_layout.addRow("الدائرة المعنية:", self.department_input)
        
        # الهامش
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل الهامش أو أي ملاحظات إضافية")
        self.notes_input.setMinimumHeight(100)
        self.add_shadow_effect(self.notes_input)
        form_layout.addRow("الهامش:", self.notes_input)
        
        main_layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر الحفظ
        self.save_button = QPushButton("حفظ البيانات")
        self.save_button.setMinimumWidth(150)
        self.save_button.clicked.connect(self.save_data)
        self.add_shadow_effect(self.save_button)
        buttons_layout.addWidget(self.save_button)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setObjectName("cancel")
        self.cancel_button.setMinimumWidth(150)
        self.cancel_button.clicked.connect(self.cancel_entry)
        self.add_shadow_effect(self.cancel_button)
        buttons_layout.addWidget(self.cancel_button)
        
        # زر العودة للقائمة الرئيسية
        self.back_button = QPushButton("العودة للقائمة الرئيسية")
        self.back_button.setMinimumWidth(150)
        self.back_button.clicked.connect(self.go_back)
        self.add_shadow_effect(self.back_button)
        buttons_layout.addWidget(self.back_button)
        
        main_layout.addLayout(buttons_layout)
    
    def add_shadow_effect(self, widget):
        """إضافة تأثير الظل للعناصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 3)
        widget.setGraphicsEffect(shadow)
    
    def show_calendar(self):
        """عرض التقويم لاختيار التاريخ"""
        self.sound_manager.play_click_sound()
        
        # إنشاء نافذة التقويم
        self.calendar_dialog = QWidget(self)
        self.calendar_dialog.setWindowFlags(Qt.Popup)
        self.calendar_dialog.setStyleSheet(f"""
            QWidget {{
                background-color: {self.theme_manager.get_color('card')};
                border: 1px solid {self.theme_manager.get_color('primary')};
                border-radius: 5px;
            }}
            QCalendarWidget {{
                background-color: {self.theme_manager.get_color('card')};
            }}
            QCalendarWidget QToolButton {{
                color: {self.theme_manager.get_color('text')};
                background-color: {self.theme_manager.get_color('primary_light')};
                border-radius: 3px;
            }}
            QCalendarWidget QMenu {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
            }}
            QCalendarWidget QSpinBox {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
            }}
            QCalendarWidget QAbstractItemView:enabled {{
                color: {self.theme_manager.get_color('text')};
                background-color: {self.theme_manager.get_color('card')};
                selection-background-color: {self.theme_manager.get_color('primary')};
                selection-color: white;
            }}
        """)
        
        layout = QVBoxLayout(self.calendar_dialog)
        
        calendar = QCalendarWidget()
        calendar.setGridVisible(True)
        calendar.clicked.connect(self.set_date_from_calendar)
        layout.addWidget(calendar)
        
        # تحديد موقع التقويم
        pos = self.year_input.mapToGlobal(self.year_input.rect().bottomLeft())
        self.calendar_dialog.move(pos)
        self.calendar_dialog.show()
    
    def set_date_from_calendar(self, date):
        """تعيين التاريخ من التقويم"""
        self.day_input.setText(str(date.day()))
        self.month_input.setText(str(date.month()))
        self.year_input.setText(str(date.year()))
        self.calendar_dialog.close()
    
    def save_data(self):
        """حفظ البيانات المدخلة"""
        self.sound_manager.play_click_sound()
        
        # التحقق من صحة البيانات
        if not self.validate_inputs():
            return
        
        # استخراج البيانات من الحقول
        doc_number = self.doc_number_input.text()
        doc_day = int(self.day_input.text())
        doc_month = int(self.month_input.text())
        doc_year = int(self.year_input.text())
        subject = self.subject_input.text()
        content = self.content_input.toPlainText()
        department = self.department_input.text()
        notes = self.notes_input.toPlainText()
        
        # حفظ البيانات في قاعدة البيانات
        if self.db_manager:
            record_id = self.db_manager.add_record(
                doc_number, doc_day, doc_month, doc_year,
                subject, content, department, notes
            )
            
            if record_id:
                self.sound_manager.play_success_sound()
                QMessageBox.information(self, "نجاح العملية", "تم حفظ البيانات بنجاح!")
                self.clear_inputs()
            else:
                self.sound_manager.play_error_sound()
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ البيانات!")
        else:
            # للاختبار فقط عندما لا تكون قاعدة البيانات متصلة
            self.sound_manager.play_success_sound()
            QMessageBox.information(self, "نجاح العملية (وضع الاختبار)", "تم حفظ البيانات بنجاح!")
            self.clear_inputs()
    
    def validate_inputs(self):
        """التحقق من صحة البيانات المدخلة"""
        # التحقق من رقم الكتاب
        if not self.doc_number_input.text():
            self.sound_manager.play_error_sound()
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال رقم الكتاب!")
            self.doc_number_input.setFocus()
            return False
        
        # التحقق من التاريخ
        try:
            day = int(self.day_input.text()) if self.day_input.text() else 0
            month = int(self.month_input.text()) if self.month_input.text() else 0
            year = int(self.year_input.text()) if self.year_input.text() else 0
            
            if not (1 <= day <= 31 and 1 <= month <= 12 and year >= 1900):
                raise ValueError("تاريخ غير صالح")
        except (ValueError, TypeError):
            self.sound_manager.play_error_sound()
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال تاريخ صالح!")
            self.day_input.setFocus()
            return False
        
        # التحقق من الموضوع
        if not self.subject_input.text():
            self.sound_manager.play_error_sound()
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال موضوع الكتاب!")
            self.subject_input.setFocus()
            return False
        
        # التحقق من الفحوى أو اسم الموظف
        if not self.content_input.toPlainText():
            self.sound_manager.play_error_sound()
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال الفحوى أو اسم الموظف المعني!")
            self.content_input.setFocus()
            return False
        
        # التحقق من الدائرة المعنية
        if not self.department_input.text():
            self.sound_manager.play_error_sound()
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم الدائرة المعنية!")
            self.department_input.setFocus()
            return False
        
        return True
    
    def clear_inputs(self):
        """مسح جميع الحقول"""
        self.doc_number_input.clear()
        self.day_input.clear()
        self.month_input.clear()
        self.year_input.clear()
        self.subject_input.clear()
        self.content_input.clear()
        self.department_input.clear()
        self.notes_input.clear()
        self.doc_number_input.setFocus()
    
    def cancel_entry(self):
        """إلغاء الإدخال ومسح الحقول"""
        self.sound_manager.play_click_sound()
        
        if (self.doc_number_input.text() or self.day_input.text() or 
            self.month_input.text() or self.year_input.text() or 
            self.subject_input.text() or self.content_input.toPlainText() or 
            self.department_input.text() or self.notes_input.toPlainText()):
            
            reply = QMessageBox.question(
                self, "تأكيد الإلغاء", 
                "هل أنت متأكد من رغبتك في إلغاء الإدخال ومسح جميع البيانات؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.clear_inputs()
        else:
            self.clear_inputs()
    
    def go_back(self):
        """العودة إلى القائمة الرئيسية"""
        self.sound_manager.play_click_sound()
        
        if (self.doc_number_input.text() or self.day_input.text() or 
            self.month_input.text() or self.year_input.text() or 
            self.subject_input.text() or self.content_input.toPlainText() or 
            self.department_input.text() or self.notes_input.toPlainText()):
            
            reply = QMessageBox.question(
                self, "تأكيد العودة", 
                "هناك بيانات لم يتم حفظها. هل أنت متأكد من رغبتك في العودة إلى القائمة الرئيسية؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.close()
        else:
            self.close()
