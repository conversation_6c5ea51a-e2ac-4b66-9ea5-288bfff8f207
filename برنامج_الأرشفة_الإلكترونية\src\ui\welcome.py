import os
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QPushButton, 
                           QVBoxLayout, QHBoxLayout, QWidget, QGraphicsDropShadowEffect)
from PyQt5.QtGui import QFont, QColor, QPixmap, QPainter, QBrush, QLinearGradient
from PyQt5.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve, QPoint, QTimer

from ..utils.themes import ThemeManager
from ..utils.sound import SoundManager

class WelcomeScreen(QMainWindow):
    """واجهة الترحيب الرئيسية للبرنامج"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        self.sound_manager = SoundManager()
        
        self.setWindowTitle("برنامج الأرشفة الإلكترونية")
        self.setMinimumSize(800, 600)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد الخلفية
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 {self.theme_manager.get_color('primary_light')}, 
                                          stop:1 {self.theme_manager.get_color('primary')});
            }}
            QPushButton {{
                background-color: {self.theme_manager.get_color('accent')};
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme_manager.get_color('accent_light')};
            }}
            QLabel#title {{
                color: white;
                font-size: 36px;
                font-weight: bold;
            }}
            QLabel#subtitle {{
                color: white;
                font-size: 18px;
            }}
        """)
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setAlignment(Qt.AlignCenter)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(50, 50, 50, 50)
        
        # شعار البرنامج
        logo_label = QLabel()
        logo_pixmap = self.create_logo()
        logo_label.setPixmap(logo_pixmap)
        logo_label.setAlignment(Qt.AlignCenter)
        
        # عنوان البرنامج
        title_label = QLabel("برنامج الأرشفة الإلكترونية")
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignCenter)
        
        # العنوان الفرعي
        subtitle_label = QLabel("نظام متكامل لإدارة وأرشفة المستندات")
        subtitle_label.setObjectName("subtitle")
        subtitle_label.setAlignment(Qt.AlignCenter)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(logo_label)
        main_layout.addWidget(title_label)
        main_layout.addWidget(subtitle_label)
        main_layout.addSpacing(30)
        
        # أزرار التنقل
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)
        
        # إنشاء الأزرار
        self.create_animated_button(buttons_layout, "إدخال بيانات جديدة", self.open_data_entry)
        self.create_animated_button(buttons_layout, "استعراض الأرشيف", self.open_archive)
        self.create_animated_button(buttons_layout, "عرض التقارير", self.open_reports)
        self.create_animated_button(buttons_layout, "تصفية البيانات", self.open_filter)
        self.create_animated_button(buttons_layout, "الإعدادات", self.open_settings)
        
        # إضافة تخطيط الأزرار إلى التخطيط الرئيسي
        main_layout.addLayout(buttons_layout)
        
        # إضافة معلومات الإصدار
        version_label = QLabel("الإصدار: 1.0.0 | إعداد: المحاسب المبرمج علي عاجل خشان المحنة")
        version_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-size: 12px;")
        version_label.setAlignment(Qt.AlignCenter)
        main_layout.addSpacing(20)
        main_layout.addWidget(version_label)
    
    def create_logo(self):
        """إنشاء شعار البرنامج"""
        # إنشاء صورة فارغة للشعار
        logo_size = 150
        logo = QPixmap(logo_size, logo_size)
        logo.fill(Qt.transparent)
        
        # رسم الشعار
        painter = QPainter(logo)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم دائرة خارجية
        painter.setPen(Qt.NoPen)
        gradient = QLinearGradient(0, 0, logo_size, logo_size)
        gradient.setColorAt(0, QColor(self.theme_manager.get_color('accent')))
        gradient.setColorAt(1, QColor(self.theme_manager.get_color('accent_dark')))
        painter.setBrush(QBrush(gradient))
        painter.drawEllipse(5, 5, logo_size-10, logo_size-10)
        
        # رسم رمز المستند
        painter.setPen(Qt.white)
        painter.setBrush(Qt.white)
        doc_width = int(logo_size * 0.6)
        doc_height = int(logo_size * 0.7)
        doc_x = int((logo_size - doc_width) / 2)
        doc_y = int((logo_size - doc_height) / 2)
        painter.drawRect(doc_x, doc_y, doc_width, doc_height)
        
        # رسم خطوط المستند
        line_spacing = int(doc_height / 6)
        painter.setPen(QColor(self.theme_manager.get_color('accent')))
        for i in range(1, 5):
            y = int(doc_y + i * line_spacing)
            painter.drawLine(int(doc_x + 10), y, int(doc_x + doc_width - 10), y)
        
        painter.end()
        return logo
    
    def create_animated_button(self, layout, text, slot):
        """إنشاء زر متحرك مع تأثيرات"""
        button = QPushButton(text)
        button.setCursor(Qt.PointingHandCursor)
        button.setMinimumWidth(300)
        
        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        button.setGraphicsEffect(shadow)
        
        # ربط الزر بالوظيفة
        button.clicked.connect(slot)
        
        # ربط تأثيرات الحركة
        button.enterEvent = lambda e, b=button: self.button_hover_enter(e, b)
        button.leaveEvent = lambda e, b=button: self.button_hover_leave(e, b)
        
        layout.addWidget(button, alignment=Qt.AlignCenter)
        return button
    
    def button_hover_enter(self, event, button):
        """تأثير عند تحويم المؤشر فوق الزر"""
        # تشغيل صوت التحويم
        self.sound_manager.play_hover_sound()
        
        # تأثير التكبير
        animation = QPropertyAnimation(button, b"geometry")
        animation.setDuration(150)
        current_geometry = button.geometry()
        target_geometry = current_geometry.adjusted(-10, -2, 10, 2)
        animation.setStartValue(current_geometry)
        animation.setEndValue(target_geometry)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        animation.start()
    
    def button_hover_leave(self, event, button):
        """تأثير عند مغادرة المؤشر للزر"""
        # تأثير التصغير
        animation = QPropertyAnimation(button, b"geometry")
        animation.setDuration(150)
        current_geometry = button.geometry()
        original_width = button.minimumWidth()
        center_x = current_geometry.center().x()
        center_y = current_geometry.center().y()
        target_geometry = QRect(
            center_x - original_width // 2,
            current_geometry.y() + 2,
            original_width,
            current_geometry.height() - 4
        )
        animation.setStartValue(current_geometry)
        animation.setEndValue(target_geometry)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        animation.start()
    
    def open_data_entry(self):
        """فتح واجهة إدخال البيانات"""
        self.sound_manager.play_click_sound()
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        print("فتح واجهة إدخال البيانات")
    
    def open_archive(self):
        """فتح واجهة الأرشيف"""
        self.sound_manager.play_click_sound()
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        print("فتح واجهة الأرشيف")
    
    def open_reports(self):
        """فتح واجهة التقارير"""
        self.sound_manager.play_click_sound()
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        print("فتح واجهة التقارير")
    
    def open_filter(self):
        """فتح واجهة التصفية"""
        self.sound_manager.play_click_sound()
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        print("فتح واجهة التصفية")
    
    def open_settings(self):
        """فتح واجهة الإعدادات"""
        self.sound_manager.play_click_sound()
        # سيتم تنفيذ هذه الوظيفة لاحقاً
        print("فتح واجهة الإعدادات")
