import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QFormLayout, QGroupBox, QSlider, QComboBox,
                           QColorDialog, QMessageBox, QGraphicsDropShadowEffect, QCheckBox)
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette
from PyQt5.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve

from ..utils.themes import ThemeManager
from ..utils.sound import SoundManager
from ..database.db_manager import DatabaseManager

class SettingsScreen(QMainWindow):
    """واجهة الإعدادات"""
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        self.sound_manager = SoundManager()
        self.db_manager = db_manager
        
        self.setWindowTitle("الإعدادات - برنامج الأرشفة الإلكترونية")
        self.setMinimumSize(800, 600)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد الخلفية والأنماط
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.theme_manager.get_color('background')};
            }}
            QLabel {{
                color: {self.theme_manager.get_color('text')};
                font-size: 14px;
            }}
            QLabel#header {{
                color: {self.theme_manager.get_color('primary')};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
            QGroupBox {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
                margin-top: 15px;
                font-weight: bold;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                color: {self.theme_manager.get_color('primary')};
            }}
            QComboBox {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }}
            QComboBox:focus {{
                border: 2px solid {self.theme_manager.get_color('primary')};
            }}
            QPushButton {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme_manager.get_color('primary_light')};
            }}
            QPushButton#color {{
                background-color: {self.theme_manager.get_color('accent')};
            }}
            QPushButton#color:hover {{
                background-color: {self.theme_manager.get_color('accent_light')};
            }}
            QPushButton#reset {{
                background-color: {self.theme_manager.get_color('warning')};
            }}
            QPushButton#reset:hover {{
                background-color: #ffb74d;
            }}
            QSlider::groove:horizontal {{
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                height: 8px;
                background: {self.theme_manager.get_color('card')};
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {self.theme_manager.get_color('primary')};
                border: 1px solid {self.theme_manager.get_color('primary_dark')};
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }}
            QCheckBox {{
                color: {self.theme_manager.get_color('text')};
                font-size: 14px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {self.theme_manager.get_color('primary')};
                border: 2px solid {self.theme_manager.get_color('primary_dark')};
            }}
        """)
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        header_label = QLabel("إعدادات البرنامج")
        header_label.setObjectName("header")
        header_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header_label)
        
        # مجموعة إعدادات السمة
        theme_group = QGroupBox("إعدادات السمة")
        theme_layout = QFormLayout(theme_group)
        theme_layout.setLabelAlignment(Qt.AlignRight)
        theme_layout.setFormAlignment(Qt.AlignRight)
        theme_layout.setSpacing(15)
        
        # اختيار السمة
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(self.theme_manager.get_available_themes())
        self.theme_combo.currentIndexChanged.connect(self.preview_theme)
        self.add_shadow_effect(self.theme_combo)
        theme_layout.addRow("السمة:", self.theme_combo)
        
        # لون الخلفية
        bg_color_layout = QHBoxLayout()
        self.bg_color_preview = QLabel()
        self.bg_color_preview.setFixedSize(30, 30)
        self.bg_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('background')}; border: 1px solid black;")
        bg_color_layout.addWidget(self.bg_color_preview)
        
        self.bg_color_button = QPushButton("تغيير لون الخلفية")
        self.bg_color_button.setObjectName("color")
        self.bg_color_button.clicked.connect(lambda: self.choose_color('background'))
        self.add_shadow_effect(self.bg_color_button)
        bg_color_layout.addWidget(self.bg_color_button)
        
        theme_layout.addRow("لون الخلفية:", bg_color_layout)
        
        # لون الأزرار
        btn_color_layout = QHBoxLayout()
        self.btn_color_preview = QLabel()
        self.btn_color_preview.setFixedSize(30, 30)
        self.btn_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('primary')}; border: 1px solid black;")
        btn_color_layout.addWidget(self.btn_color_preview)
        
        self.btn_color_button = QPushButton("تغيير لون الأزرار")
        self.btn_color_button.setObjectName("color")
        self.btn_color_button.clicked.connect(lambda: self.choose_color('primary'))
        self.add_shadow_effect(self.btn_color_button)
        btn_color_layout.addWidget(self.btn_color_button)
        
        theme_layout.addRow("لون الأزرار:", btn_color_layout)
        
        # لون النص
        text_color_layout = QHBoxLayout()
        self.text_color_preview = QLabel()
        self.text_color_preview.setFixedSize(30, 30)
        self.text_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('text')}; border: 1px solid black;")
        text_color_layout.addWidget(self.text_color_preview)
        
        self.text_color_button = QPushButton("تغيير لون النص")
        self.text_color_button.setObjectName("color")
        self.text_color_button.clicked.connect(lambda: self.choose_color('text'))
        self.add_shadow_effect(self.text_color_button)
        text_color_layout.addWidget(self.text_color_button)
        
        theme_layout.addRow("لون النص:", text_color_layout)
        
        main_layout.addWidget(theme_group)
        
        # مجموعة إعدادات الصوت
        sound_group = QGroupBox("إعدادات الصوت")
        sound_layout = QFormLayout(sound_group)
        sound_layout.setLabelAlignment(Qt.AlignRight)
        sound_layout.setFormAlignment(Qt.AlignRight)
        sound_layout.setSpacing(15)
        
        # تفعيل المؤثرات الصوتية
        self.sound_checkbox = QCheckBox("تفعيل المؤثرات الصوتية")
        self.sound_checkbox.setChecked(True)
        self.sound_checkbox.stateChanged.connect(self.toggle_sound)
        sound_layout.addRow("", self.sound_checkbox)
        
        # مستوى الصوت
        volume_layout = QHBoxLayout()
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_slider.setTickPosition(QSlider.TicksBelow)
        self.volume_slider.setTickInterval(10)
        self.volume_slider.valueChanged.connect(self.change_volume)
        volume_layout.addWidget(self.volume_slider)
        
        self.volume_label = QLabel("50%")
        volume_layout.addWidget(self.volume_label)
        
        sound_layout.addRow("مستوى الصوت:", volume_layout)
        
        main_layout.addWidget(sound_group)
        
        # مجموعة إعدادات الرسوم المتحركة
        animation_group = QGroupBox("إعدادات الرسوم المتحركة")
        animation_layout = QFormLayout(animation_group)
        animation_layout.setLabelAlignment(Qt.AlignRight)
        animation_layout.setFormAlignment(Qt.AlignRight)
        animation_layout.setSpacing(15)
        
        # تفعيل الرسوم المتحركة
        self.animation_checkbox = QCheckBox("تفعيل الرسوم المتحركة")
        self.animation_checkbox.setChecked(True)
        animation_layout.addRow("", self.animation_checkbox)
        
        main_layout.addWidget(animation_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر حفظ الإعدادات
        self.save_button = QPushButton("حفظ الإعدادات")
        self.save_button.setIcon(QIcon.fromTheme("document-save"))
        self.save_button.setIconSize(QSize(20, 20))
        self.save_button.setMinimumWidth(150)
        self.save_button.clicked.connect(self.save_settings)
        self.add_shadow_effect(self.save_button)
        buttons_layout.addWidget(self.save_button)
        
        # زر إعادة تعيين
        self.reset_button = QPushButton("استعادة الإعدادات الافتراضية")
        self.reset_button.setObjectName("reset")
        self.reset_button.setIcon(QIcon.fromTheme("edit-clear"))
        self.reset_button.setIconSize(QSize(20, 20))
        self.reset_button.setMinimumWidth(150)
        self.reset_button.clicked.connect(self.reset_settings)
        self.add_shadow_effect(self.reset_button)
        buttons_layout.addWidget(self.reset_button)
        
        # زر العودة للقائمة الرئيسية
        self.back_button = QPushButton("العودة للقائمة الرئيسية")
        self.back_button.setIcon(QIcon.fromTheme("go-home"))
        self.back_button.setIconSize(QSize(20, 20))
        self.back_button.setMinimumWidth(150)
        self.back_button.clicked.connect(self.go_back)
        self.add_shadow_effect(self.back_button)
        buttons_layout.addWidget(self.back_button)
        
        main_layout.addLayout(buttons_layout)
    
    def add_shadow_effect(self, widget):
        """إضافة تأثير الظل للعناصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 3)
        widget.setGraphicsEffect(shadow)
    
    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        if self.db_manager:
            settings = self.db_manager.get_settings()
            if settings:
                # تحديث واجهة الإعدادات بالقيم المحفوظة
                theme_index = self.theme_combo.findText(settings['theme_name'])
                if theme_index >= 0:
                    self.theme_combo.setCurrentIndex(theme_index)
                
                self.bg_color_preview.setStyleSheet(f"background-color: {settings['background_color']}; border: 1px solid black;")
                self.btn_color_preview.setStyleSheet(f"background-color: {settings['button_color']}; border: 1px solid black;")
                self.text_color_preview.setStyleSheet(f"background-color: {settings['text_color']}; border: 1px solid black;")
                
                self.sound_checkbox.setChecked(bool(settings['sound_effects']))
                self.animation_checkbox.setChecked(bool(settings['animations']))
                
                # في التطبيق الفعلي، يمكن تحميل مستوى الصوت أيضاً
                # self.volume_slider.setValue(settings['volume'])
                # self.volume_label.setText(f"{settings['volume']}%")
    
    def preview_theme(self, index):
        """معاينة السمة المختارة"""
        theme_name = self.theme_combo.currentText()
        self.theme_manager.set_theme(theme_name)
        
        # تحديث مربعات معاينة الألوان
        self.bg_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('background')}; border: 1px solid black;")
        self.btn_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('primary')}; border: 1px solid black;")
        self.text_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('text')}; border: 1px solid black;")
        
        # تشغيل صوت التغيير
        self.sound_manager.play_click_sound()
    
    def choose_color(self, color_type):
        """اختيار لون مخصص"""
        self.sound_manager.play_click_sound()
        
        # تحديد اللون الحالي
        current_color = QColor(self.theme_manager.get_color(color_type))
        
        # فتح مربع حوار اختيار اللون
        color = QColorDialog.getColor(current_color, self, "اختيار لون")
        
        if color.isValid():
            # تحديث مربع معاينة اللون
            if color_type == 'background':
                self.bg_color_preview.setStyleSheet(f"background-color: {color.name()}; border: 1px solid black;")
            elif color_type == 'primary':
                self.btn_color_preview.setStyleSheet(f"background-color: {color.name()}; border: 1px solid black;")
            elif color_type == 'text':
                self.text_color_preview.setStyleSheet(f"background-color: {color.name()}; border: 1px solid black;")
    
    def toggle_sound(self, state):
        """تفعيل أو تعطيل المؤثرات الصوتية"""
        enabled = state == Qt.Checked
        self.sound_manager.enable_sounds(enabled)
        
        # تفعيل أو تعطيل شريط مستوى الصوت
        self.volume_slider.setEnabled(enabled)
        self.volume_label.setEnabled(enabled)
    
    def change_volume(self, value):
        """تغيير مستوى الصوت"""
        self.volume_label.setText(f"{value}%")
        self.sound_manager.set_volume(value)
        
        # تشغيل صوت للاختبار
        if value % 10 == 0 and self.sound_checkbox.isChecked():
            self.sound_manager.play_click_sound()
    
    def save_settings(self):
        """حفظ الإعدادات"""
        self.sound_manager.play_click_sound()
        
        theme_name = self.theme_combo.currentText()
        background_color = self.bg_color_preview.styleSheet().split("background-color: ")[1].split(";")[0]
        button_color = self.btn_color_preview.styleSheet().split("background-color: ")[1].split(";")[0]
        text_color = self.text_color_preview.styleSheet().split("background-color: ")[1].split(";")[0]
        sound_effects = int(self.sound_checkbox.isChecked())
        animations = int(self.animation_checkbox.isChecked())
        
        if self.db_manager:
            success = self.db_manager.update_settings(
                theme_name, background_color, button_color, text_color, sound_effects, animations
            )
            
            if success:
                self.sound_manager.play_success_sound()
                QMessageBox.information(self, "نجاح العملية", "تم حفظ الإعدادات بنجاح!")
            else:
                self.sound_manager.play_error_sound()
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ الإعدادات!")
        else:
            # للاختبار فقط عندما لا تكون قاعدة البيانات متصلة
            self.sound_manager.play_success_sound()
            QMessageBox.information(self, "نجاح العملية (وضع الاختبار)", "تم حفظ الإعدادات بنجاح!")
    
    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        self.sound_manager.play_click_sound()
        
        reply = QMessageBox.question(
            self, "تأكيد الإعادة", 
            "هل أنت متأكد من رغبتك في استعادة الإعدادات الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إعادة تعيين السمة إلى الافتراضية
            self.theme_combo.setCurrentText("default")
            
            # إعادة تعيين الألوان
            self.bg_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('background')}; border: 1px solid black;")
            self.btn_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('primary')}; border: 1px solid black;")
            self.text_color_preview.setStyleSheet(f"background-color: {self.theme_manager.get_color('text')}; border: 1px solid black;")
            
            # إعادة تعيين إعدادات الصوت
            self.sound_checkbox.setChecked(True)
            self.volume_slider.setValue(50)
            self.volume_label.setText("50%")
            
            # إعادة تعيين إعدادات الرسوم المتحركة
            self.animation_checkbox.setChecked(True)
            
            self.sound_manager.play_success_sound()
            QMessageBox.information(self, "نجاح العملية", "تم استعادة الإعدادات الافتراضية!")
    
    def go_back(self):
        """العودة إلى القائمة الرئيسية"""
        self.sound_manager.play_click_sound()
        
        # التحقق من وجود تغييرات غير محفوظة
        # في التطبيق الفعلي، يمكن مقارنة الإعدادات الحالية مع الإعدادات المحفوظة
        
        self.close()
