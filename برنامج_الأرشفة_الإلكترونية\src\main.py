import os
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QSplashScreen
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt, QTimer

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from src.ui.welcome import WelcomeScreen
    from src.ui.data_entry import DataEntryScreen
    from src.ui.report import ReportScreen
    from src.ui.archive import ArchiveScreen
    from src.ui.filter import FilterScreen
    from src.ui.settings import SettingsScreen
    from src.database.db_manager import DatabaseManager
    from src.utils.themes import ThemeManager
    from src.utils.sound import SoundManager
except ImportError:
    # إنشاء فئات وهمية في حالة عدم وجود الوحدات
    class WelcomeScreen:
        def __init__(self):
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton
            self.widget = QWidget()
            self.widget.setWindowTitle("برنامج الأرشفة الإلكترونية")
            self.widget.setGeometry(100, 100, 800, 600)

            layout = QVBoxLayout()
            label = QLabel("مرحباً بك في برنامج الأرشفة الإلكترونية")
            label.setStyleSheet("font-size: 24px; font-weight: bold; text-align: center;")
            layout.addWidget(label)

            self.widget.setLayout(layout)

        def show(self):
            self.widget.show()

        def hide(self):
            self.widget.hide()

        def isVisible(self):
            return self.widget.isVisible()

    class DataEntryScreen:
        def __init__(self, db_manager=None):
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
            self.widget = QWidget()
            self.widget.setWindowTitle("إدخال البيانات")
            self.widget.setGeometry(100, 100, 800, 600)

            layout = QVBoxLayout()
            label = QLabel("واجهة إدخال البيانات")
            layout.addWidget(label)
            self.widget.setLayout(layout)

        def show(self):
            self.widget.show()

        def hide(self):
            self.widget.hide()

        def isVisible(self):
            return self.widget.isVisible()

    class ReportScreen:
        def __init__(self, db_manager=None):
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
            self.widget = QWidget()
            self.widget.setWindowTitle("التقارير")
            self.widget.setGeometry(100, 100, 800, 600)

            layout = QVBoxLayout()
            label = QLabel("واجهة التقارير")
            layout.addWidget(label)
            self.widget.setLayout(layout)

        def show(self):
            self.widget.show()

        def hide(self):
            self.widget.hide()

        def isVisible(self):
            return self.widget.isVisible()

    class ArchiveScreen:
        def __init__(self, db_manager=None):
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
            self.widget = QWidget()
            self.widget.setWindowTitle("الأرشيف")
            self.widget.setGeometry(100, 100, 800, 600)

            layout = QVBoxLayout()
            label = QLabel("واجهة الأرشيف")
            layout.addWidget(label)
            self.widget.setLayout(layout)

        def show(self):
            self.widget.show()

        def hide(self):
            self.widget.hide()

        def isVisible(self):
            return self.widget.isVisible()

    class FilterScreen:
        def __init__(self, db_manager=None):
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
            self.widget = QWidget()
            self.widget.setWindowTitle("التصفية")
            self.widget.setGeometry(100, 100, 800, 600)

            layout = QVBoxLayout()
            label = QLabel("واجهة التصفية")
            layout.addWidget(label)
            self.widget.setLayout(layout)

        def show(self):
            self.widget.show()

        def hide(self):
            self.widget.hide()

        def isVisible(self):
            return self.widget.isVisible()

    class SettingsScreen:
        def __init__(self, db_manager=None):
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
            self.widget = QWidget()
            self.widget.setWindowTitle("الإعدادات")
            self.widget.setGeometry(100, 100, 800, 600)

            layout = QVBoxLayout()
            label = QLabel("واجهة الإعدادات")
            layout.addWidget(label)
            self.widget.setLayout(layout)

        def show(self):
            self.widget.show()

        def hide(self):
            self.widget.hide()

        def isVisible(self):
            return self.widget.isVisible()

    class DatabaseManager:
        def __init__(self, db_path):
            self.db_path = db_path
            print(f"تم إنشاء مدير قاعدة البيانات: {db_path}")

        def close(self):
            print("تم إغلاق قاعدة البيانات")

    class ThemeManager:
        def __init__(self):
            print("تم تهيئة مدير السمات")

    class SoundManager:
        def __init__(self):
            print("تم تهيئة مدير الصوت")

class MainApplication:
    """التطبيق الرئيسي لبرنامج الأرشفة الإلكترونية"""
    
    def __init__(self):
        """تهيئة التطبيق الرئيسي"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("برنامج الأرشفة الإلكترونية")
        self.app.setApplicationVersion("1.0.0")
        
        # تحديد مسار قاعدة البيانات
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        db_path = os.path.join(base_dir, 'data', 'database.db')
        
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # تهيئة مدير قاعدة البيانات
        self.db_manager = DatabaseManager(db_path)
        
        # تهيئة مدير السمات
        self.theme_manager = ThemeManager()
        
        # تهيئة مدير الصوت
        self.sound_manager = SoundManager()
        
        # تهيئة النوافذ
        self.welcome_screen = None
        self.data_entry_screen = None
        self.report_screen = None
        self.archive_screen = None
        self.filter_screen = None
        self.settings_screen = None
        
        # عرض شاشة البداية
        self.show_splash_screen()
    
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        # إنشاء صورة شاشة البداية
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.transparent)
        
        # إنشاء شاشة البداية
        splash = QSplashScreen(splash_pixmap, Qt.WindowStaysOnTopHint)
        splash.show()
        
        # تأخير لعرض شاشة البداية
        QTimer.singleShot(1500, lambda: self.show_welcome_screen(splash))
    
    def show_welcome_screen(self, splash=None):
        """عرض واجهة الترحيب"""
        self.welcome_screen = WelcomeScreen()

        # ربط أزرار التنقل بالوظائف المناسبة
        self.welcome_screen.open_data_entry = self.show_data_entry_screen
        self.welcome_screen.open_archive = self.show_archive_screen
        self.welcome_screen.open_reports = self.show_report_screen
        self.welcome_screen.open_filter = self.show_filter_screen
        self.welcome_screen.open_settings = self.show_settings_screen

        # إخفاء شاشة البداية وعرض واجهة الترحيب
        if splash:
            if hasattr(self.welcome_screen, 'widget'):
                splash.finish(self.welcome_screen.widget)
            else:
                splash.finish(self.welcome_screen)

        self.welcome_screen.show()
    
    def show_data_entry_screen(self):
        """عرض واجهة إدخال البيانات"""
        if not self.data_entry_screen:
            self.data_entry_screen = DataEntryScreen(db_manager=self.db_manager)
            self.data_entry_screen.go_back = self.return_to_welcome_screen
        
        self.welcome_screen.hide()
        self.data_entry_screen.show()
    
    def show_report_screen(self):
        """عرض واجهة التقارير"""
        if not self.report_screen:
            self.report_screen = ReportScreen(db_manager=self.db_manager)
            self.report_screen.go_back = self.return_to_welcome_screen
        
        self.welcome_screen.hide()
        self.report_screen.show()
    
    def show_archive_screen(self):
        """عرض واجهة الأرشيف"""
        if not self.archive_screen:
            self.archive_screen = ArchiveScreen(db_manager=self.db_manager)
            self.archive_screen.go_back = self.return_to_welcome_screen
        
        self.welcome_screen.hide()
        self.archive_screen.show()
    
    def show_filter_screen(self):
        """عرض واجهة التصفية"""
        if not self.filter_screen:
            self.filter_screen = FilterScreen(db_manager=self.db_manager)
            self.filter_screen.go_back = self.return_to_welcome_screen
        
        self.welcome_screen.hide()
        self.filter_screen.show()
    
    def show_settings_screen(self):
        """عرض واجهة الإعدادات"""
        if not self.settings_screen:
            self.settings_screen = SettingsScreen(db_manager=self.db_manager)
            self.settings_screen.go_back = self.return_to_welcome_screen
        
        self.welcome_screen.hide()
        self.settings_screen.show()
    
    def return_to_welcome_screen(self):
        """العودة إلى واجهة الترحيب"""
        # إخفاء جميع النوافذ المفتوحة
        if self.data_entry_screen and self.data_entry_screen.isVisible():
            self.data_entry_screen.hide()
        
        if self.report_screen and self.report_screen.isVisible():
            self.report_screen.hide()
        
        if self.archive_screen and self.archive_screen.isVisible():
            self.archive_screen.hide()
        
        if self.filter_screen and self.filter_screen.isVisible():
            self.filter_screen.hide()
        
        if self.settings_screen and self.settings_screen.isVisible():
            self.settings_screen.hide()
        
        # عرض واجهة الترحيب
        self.welcome_screen.show()
    
    def run(self):
        """تشغيل التطبيق"""
        return self.app.exec_()
    
    def cleanup(self):
        """تنظيف الموارد قبل إغلاق التطبيق"""
        if self.db_manager:
            self.db_manager.close()

def main():
    """نقطة الدخول الرئيسية للبرنامج"""
    app = MainApplication()
    exit_code = app.run()
    app.cleanup()
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
