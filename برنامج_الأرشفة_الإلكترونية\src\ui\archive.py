import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QTableWidget, QTableWidgetItem, QPushButton, QHeaderView,
                           QMessageBox, QGraphicsDropShadowEffect, QAbstractItemView,
                           QLineEdit, QComboBox)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve

from ..utils.themes import ThemeManager
from ..utils.sound import SoundManager
from ..database.db_manager import DatabaseManager

class ArchiveScreen(QMainWindow):
    """واجهة الأرشيف العام"""
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        self.sound_manager = SoundManager()
        self.db_manager = db_manager
        
        self.current_record_index = 0
        self.records = []
        
        self.setWindowTitle("الأرشيف العام - برنامج الأرشفة الإلكترونية")
        self.setMinimumSize(900, 700)
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد الخلفية والأنماط
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.theme_manager.get_color('background')};
            }}
            QLabel {{
                color: {self.theme_manager.get_color('text')};
                font-size: 14px;
            }}
            QLabel#header {{
                color: {self.theme_manager.get_color('primary')};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
            QTableWidget {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                gridline-color: {self.theme_manager.get_color('primary_light')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
            }}
            QTableWidget::item:selected {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
            }}
            QHeaderView::section {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                padding: 5px;
                border: 1px solid {self.theme_manager.get_color('primary_dark')};
                font-weight: bold;
            }}
            QPushButton {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme_manager.get_color('primary_light')};
            }}
            QPushButton#delete {{
                background-color: {self.theme_manager.get_color('error')};
            }}
            QPushButton#delete:hover {{
                background-color: #ff5252;
            }}
            QPushButton#edit {{
                background-color: {self.theme_manager.get_color('accent')};
            }}
            QPushButton#edit:hover {{
                background-color: {self.theme_manager.get_color('accent_light')};
            }}
            QLineEdit, QComboBox {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }}
            QLineEdit:focus, QComboBox:focus {{
                border: 2px solid {self.theme_manager.get_color('primary')};
            }}
        """)
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        header_label = QLabel("الأرشيف العام")
        header_label.setObjectName("header")
        header_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header_label)
        
        # قسم البحث
        search_layout = QHBoxLayout()
        search_layout.setSpacing(10)
        
        search_label = QLabel("بحث:")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("أدخل كلمة للبحث...")
        self.search_input.setMinimumWidth(300)
        self.search_input.textChanged.connect(self.search_records)
        self.add_shadow_effect(self.search_input)
        search_layout.addWidget(self.search_input)
        
        search_by_label = QLabel("بحث في:")
        search_layout.addWidget(search_by_label)
        
        self.search_by_combo = QComboBox()
        self.search_by_combo.addItems(["الكل", "رقم الكتاب", "الموضوع", "الموظف", "الدائرة"])
        self.search_by_combo.currentIndexChanged.connect(self.search_records)
        self.add_shadow_effect(self.search_by_combo)
        search_layout.addWidget(self.search_by_combo)
        
        search_layout.addStretch()
        
        main_layout.addLayout(search_layout)
        
        # جدول البيانات
        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            "رقم الكتاب", "تاريخ الكتاب", "الموضوع", 
            "الفحوى/الموظف", "الدائرة المعنية", "الهامش",
            "تاريخ الإنشاء", "آخر تعديل"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.verticalHeader().setVisible(True)
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.itemSelectionChanged.connect(self.update_navigation_buttons)
        self.add_shadow_effect(self.table)
        main_layout.addWidget(self.table)
        
        # أزرار التنقل
        nav_layout = QHBoxLayout()
        nav_layout.setSpacing(10)
        
        # زر القيد الأول
        self.first_button = QPushButton("القيد الأول")
        self.first_button.setIcon(QIcon.fromTheme("go-first"))
        self.first_button.setIconSize(QSize(20, 20))
        self.first_button.clicked.connect(self.go_to_first)
        self.add_shadow_effect(self.first_button)
        nav_layout.addWidget(self.first_button)
        
        # زر السابق
        self.prev_button = QPushButton("السابق")
        self.prev_button.setIcon(QIcon.fromTheme("go-previous"))
        self.prev_button.setIconSize(QSize(20, 20))
        self.prev_button.clicked.connect(self.go_to_prev)
        self.add_shadow_effect(self.prev_button)
        nav_layout.addWidget(self.prev_button)
        
        # زر التالي
        self.next_button = QPushButton("التالي")
        self.next_button.setIcon(QIcon.fromTheme("go-next"))
        self.next_button.setIconSize(QSize(20, 20))
        self.next_button.clicked.connect(self.go_to_next)
        self.add_shadow_effect(self.next_button)
        nav_layout.addWidget(self.next_button)
        
        # زر القيد الأخير
        self.last_button = QPushButton("القيد الأخير")
        self.last_button.setIcon(QIcon.fromTheme("go-last"))
        self.last_button.setIconSize(QSize(20, 20))
        self.last_button.clicked.connect(self.go_to_last)
        self.add_shadow_effect(self.last_button)
        nav_layout.addWidget(self.last_button)
        
        main_layout.addLayout(nav_layout)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(15)
        
        # زر تعديل قيد
        self.edit_button = QPushButton("تعديل قيد")
        self.edit_button.setObjectName("edit")
        self.edit_button.setIcon(QIcon.fromTheme("document-edit"))
        self.edit_button.setIconSize(QSize(20, 20))
        self.edit_button.setMinimumWidth(150)
        self.edit_button.clicked.connect(self.edit_record)
        self.add_shadow_effect(self.edit_button)
        actions_layout.addWidget(self.edit_button)
        
        # زر حذف قيد
        self.delete_button = QPushButton("حذف قيد")
        self.delete_button.setObjectName("delete")
        self.delete_button.setIcon(QIcon.fromTheme("edit-delete"))
        self.delete_button.setIconSize(QSize(20, 20))
        self.delete_button.setMinimumWidth(150)
        self.delete_button.clicked.connect(self.delete_record)
        self.add_shadow_effect(self.delete_button)
        actions_layout.addWidget(self.delete_button)
        
        # زر العودة للقائمة الرئيسية
        self.back_button = QPushButton("العودة للقائمة الرئيسية")
        self.back_button.setIcon(QIcon.fromTheme("go-home"))
        self.back_button.setIconSize(QSize(20, 20))
        self.back_button.setMinimumWidth(150)
        self.back_button.clicked.connect(self.go_back)
        self.add_shadow_effect(self.back_button)
        actions_layout.addWidget(self.back_button)
        
        main_layout.addLayout(actions_layout)
    
    def add_shadow_effect(self, widget):
        """إضافة تأثير الظل للعناصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 3)
        widget.setGraphicsEffect(shadow)
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        if self.db_manager:
            self.records = self.db_manager.get_all_records()
        else:
            # بيانات تجريبية للاختبار
            self.records = [
                {
                    'id': 1,
                    'document_number': '123/2025',
                    'document_day': 15,
                    'document_month': 5,
                    'document_year': 2025,
                    'subject': 'طلب موافقة',
                    'content_or_employee': 'محمد أحمد',
                    'department': 'قسم الموارد البشرية',
                    'notes': 'تمت الموافقة',
                    'creation_date': '2025-05-15 10:30:00',
                    'last_modified': '2025-05-15 10:30:00'
                },
                {
                    'id': 2,
                    'document_number': '124/2025',
                    'document_day': 16,
                    'document_month': 5,
                    'document_year': 2025,
                    'subject': 'طلب إجازة',
                    'content_or_employee': 'علي محمود',
                    'department': 'قسم المحاسبة',
                    'notes': 'قيد المراجعة',
                    'creation_date': '2025-05-16 11:45:00',
                    'last_modified': '2025-05-16 11:45:00'
                }
            ]
        
        self.display_records(self.records)
        self.update_navigation_buttons()
    
    def display_records(self, records):
        """عرض السجلات في الجدول"""
        # تعيين عدد الصفوف
        self.table.setRowCount(len(records))
        
        # ملء الجدول بالبيانات
        for row, record in enumerate(records):
            # رقم الكتاب
            self.table.setItem(row, 0, QTableWidgetItem(record['document_number']))
            
            # تاريخ الكتاب
            date_str = f"{record['document_day']}/{record['document_month']}/{record['document_year']}"
            self.table.setItem(row, 1, QTableWidgetItem(date_str))
            
            # الموضوع
            self.table.setItem(row, 2, QTableWidgetItem(record['subject']))
            
            # الفحوى/الموظف
            self.table.setItem(row, 3, QTableWidgetItem(record['content_or_employee']))
            
            # الدائرة المعنية
            self.table.setItem(row, 4, QTableWidgetItem(record['department']))
            
            # الهامش
            self.table.setItem(row, 5, QTableWidgetItem(record['notes']))
            
            # تاريخ الإنشاء
            self.table.setItem(row, 6, QTableWidgetItem(record['creation_date']))
            
            # آخر تعديل
            self.table.setItem(row, 7, QTableWidgetItem(record['last_modified']))
    
    def search_records(self):
        """البحث في السجلات"""
        search_text = self.search_input.text().strip()
        search_by = self.search_by_combo.currentText()
        
        if not search_text:
            self.display_records(self.records)
            return
        
        filtered_records = []
        
        for record in self.records:
            if search_by == "الكل":
                if (search_text.lower() in record['document_number'].lower() or
                    search_text.lower() in record['subject'].lower() or
                    search_text.lower() in record['content_or_employee'].lower() or
                    search_text.lower() in record['department'].lower() or
                    search_text.lower() in record['notes'].lower()):
                    filtered_records.append(record)
            elif search_by == "رقم الكتاب":
                if search_text.lower() in record['document_number'].lower():
                    filtered_records.append(record)
            elif search_by == "الموضوع":
                if search_text.lower() in record['subject'].lower():
                    filtered_records.append(record)
            elif search_by == "الموظف":
                if search_text.lower() in record['content_or_employee'].lower():
                    filtered_records.append(record)
            elif search_by == "الدائرة":
                if search_text.lower() in record['department'].lower():
                    filtered_records.append(record)
        
        self.display_records(filtered_records)
    
    def update_navigation_buttons(self):
        """تحديث حالة أزرار التنقل"""
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0
        
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        
        row_count = self.table.rowCount()
        
        if row_count == 0:
            self.first_button.setEnabled(False)
            self.prev_button.setEnabled(False)
            self.next_button.setEnabled(False)
            self.last_button.setEnabled(False)
            return
        
        current_row = self.table.currentRow()
        
        self.first_button.setEnabled(current_row > 0)
        self.prev_button.setEnabled(current_row > 0)
        self.next_button.setEnabled(current_row < row_count - 1)
        self.last_button.setEnabled(current_row < row_count - 1)
    
    def go_to_first(self):
        """الانتقال إلى القيد الأول"""
        self.sound_manager.play_click_sound()
        if self.table.rowCount() > 0:
            self.table.selectRow(0)
    
    def go_to_prev(self):
        """الانتقال إلى القيد السابق"""
        self.sound_manager.play_click_sound()
        current_row = self.table.currentRow()
        if current_row > 0:
            self.table.selectRow(current_row - 1)
    
    def go_to_next(self):
        """الانتقال إلى القيد التالي"""
        self.sound_manager.play_click_sound()
        current_row = self.table.currentRow()
        if current_row < self.table.rowCount() - 1:
            self.table.selectRow(current_row + 1)
    
    def go_to_last(self):
        """الانتقال إلى القيد الأخير"""
        self.sound_manager.play_click_sound()
        if self.table.rowCount() > 0:
            self.table.selectRow(self.table.rowCount() - 1)
    
    def edit_record(self):
        """تعديل القيد المحدد"""
        self.sound_manager.play_click_sound()
        
        selected_row = self.table.currentRow()
        if selected_row >= 0:
            # في التطبيق الفعلي، يمكن فتح نافذة تعديل القيد
            QMessageBox.information(self, "تعديل القيد", "سيتم فتح نافذة تعديل القيد في الإصدار القادم.")
    
    def delete_record(self):
        """حذف القيد المحدد"""
        self.sound_manager.play_click_sound()
        
        selected_row = self.table.currentRow()
        if selected_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف", 
                "هل أنت متأكد من رغبتك في حذف هذا القيد؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if self.db_manager:
                    record_id = self.records[selected_row]['id']
                    if self.db_manager.delete_record(record_id):
                        self.table.removeRow(selected_row)
                        self.records.pop(selected_row)
                        self.sound_manager.play_success_sound()
                        QMessageBox.information(self, "نجاح العملية", "تم حذف القيد بنجاح!")
                    else:
                        self.sound_manager.play_error_sound()
                        QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف القيد!")
                else:
                    # للاختبار فقط عندما لا تكون قاعدة البيانات متصلة
                    self.table.removeRow(selected_row)
                    self.records.pop(selected_row)
                    self.sound_manager.play_success_sound()
                    QMessageBox.information(self, "نجاح العملية (وضع الاختبار)", "تم حذف القيد بنجاح!")
    
    def go_back(self):
        """العودة إلى القائمة الرئيسية"""
        self.sound_manager.play_click_sound()
        self.close()
