import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    """مدير قاعدة البيانات للتعامل مع عمليات الحفظ والاسترجاع"""
    
    def __init__(self, db_path):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        self.connection = None
        self.cursor = None
        self.connect()
        self.create_tables()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            self.cursor = self.connection.cursor()
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
    
    def create_tables(self):
        """إنشاء الجداول اللازمة إذا لم تكن موجودة"""
        try:
            # جدول السجلات
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_number TEXT,
                document_day INTEGER,
                document_month INTEGER,
                document_year INTEGER,
                subject TEXT,
                content_or_employee TEXT,
                department TEXT,
                notes TEXT,
                creation_date TEXT,
                last_modified TEXT
            )
            ''')
            
            # جدول الإعدادات
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                theme_name TEXT,
                background_color TEXT,
                button_color TEXT,
                text_color TEXT,
                sound_effects INTEGER,
                animations INTEGER
            )
            ''')
            
            # إدخال إعدادات افتراضية إذا لم تكن موجودة
            self.cursor.execute("SELECT COUNT(*) FROM settings")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                INSERT INTO settings (theme_name, background_color, button_color, text_color, sound_effects, animations)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', ('default', '#f0f0f0', '#4a86e8', '#333333', 1, 1))
            
            self.connection.commit()
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الجداول: {e}")
    
    def add_record(self, document_number, document_day, document_month, document_year, 
                  subject, content_or_employee, department, notes):
        """إضافة سجل جديد"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.cursor.execute('''
            INSERT INTO records (
                document_number, document_day, document_month, document_year,
                subject, content_or_employee, department, notes,
                creation_date, last_modified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                document_number, document_day, document_month, document_year,
                subject, content_or_employee, department, notes,
                current_time, current_time
            ))
            self.connection.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة السجل: {e}")
            return None
    
    def update_record(self, record_id, document_number, document_day, document_month, document_year, 
                     subject, content_or_employee, department, notes):
        """تحديث سجل موجود"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.cursor.execute('''
            UPDATE records SET
                document_number = ?,
                document_day = ?,
                document_month = ?,
                document_year = ?,
                subject = ?,
                content_or_employee = ?,
                department = ?,
                notes = ?,
                last_modified = ?
            WHERE id = ?
            ''', (
                document_number, document_day, document_month, document_year,
                subject, content_or_employee, department, notes,
                current_time, record_id
            ))
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث السجل: {e}")
            return False
    
    def delete_record(self, record_id):
        """حذف سجل"""
        try:
            self.cursor.execute("DELETE FROM records WHERE id = ?", (record_id,))
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف السجل: {e}")
            return False
    
    def get_record(self, record_id):
        """استرجاع سجل واحد بواسطة المعرف"""
        try:
            self.cursor.execute("SELECT * FROM records WHERE id = ?", (record_id,))
            return dict(self.cursor.fetchone())
        except (sqlite3.Error, TypeError) as e:
            print(f"خطأ في استرجاع السجل: {e}")
            return None
    
    def get_all_records(self):
        """استرجاع جميع السجلات"""
        try:
            self.cursor.execute("SELECT * FROM records ORDER BY id DESC")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع السجلات: {e}")
            return []
    
    def filter_records(self, start_date=None, end_date=None, subject=None, 
                      employee=None, department=None, has_notes=None):
        """تصفية السجلات حسب المعايير المحددة"""
        try:
            query = "SELECT * FROM records WHERE 1=1"
            params = []
            
            if start_date:
                start_day, start_month, start_year = start_date
                query += " AND (document_year > ? OR (document_year = ? AND document_month > ?) OR (document_year = ? AND document_month = ? AND document_day >= ?))"
                params.extend([start_year, start_year, start_month, start_year, start_month, start_day])
            
            if end_date:
                end_day, end_month, end_year = end_date
                query += " AND (document_year < ? OR (document_year = ? AND document_month < ?) OR (document_year = ? AND document_month = ? AND document_day <= ?))"
                params.extend([end_year, end_year, end_month, end_year, end_month, end_day])
            
            if subject:
                query += " AND subject LIKE ?"
                params.append(f"%{subject}%")
            
            if employee:
                query += " AND content_or_employee LIKE ?"
                params.append(f"%{employee}%")
            
            if department:
                query += " AND department LIKE ?"
                params.append(f"%{department}%")
            
            if has_notes is not None:
                if has_notes:
                    query += " AND notes IS NOT NULL AND notes != ''"
                else:
                    query += " AND (notes IS NULL OR notes = '')"
            
            query += " ORDER BY document_year DESC, document_month DESC, document_day DESC"
            
            self.cursor.execute(query, params)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في تصفية السجلات: {e}")
            return []
    
    def get_settings(self):
        """استرجاع إعدادات البرنامج"""
        try:
            self.cursor.execute("SELECT * FROM settings WHERE id = 1")
            return dict(self.cursor.fetchone())
        except (sqlite3.Error, TypeError) as e:
            print(f"خطأ في استرجاع الإعدادات: {e}")
            return None
    
    def update_settings(self, theme_name, background_color, button_color, text_color, sound_effects, animations):
        """تحديث إعدادات البرنامج"""
        try:
            self.cursor.execute('''
            UPDATE settings SET
                theme_name = ?,
                background_color = ?,
                button_color = ?,
                text_color = ?,
                sound_effects = ?,
                animations = ?
            WHERE id = 1
            ''', (theme_name, background_color, button_color, text_color, sound_effects, animations))
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث الإعدادات: {e}")
            return False
