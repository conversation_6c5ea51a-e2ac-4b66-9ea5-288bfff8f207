#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إنشاء ملف تنفيذي لبرنامج الأرشفة الإلكترونية
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_requirements():
    """تثبيت المتطلبات اللازمة"""
    requirements = [
        'PyQt5>=5.15.0',
        'PyInstaller>=4.0',
        'Pillow>=8.0.0'
    ]
    
    print("🔧 تثبيت المتطلبات...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            print(f"✅ تم تثبيت {req}")
        except subprocess.CalledProcessError:
            print(f"❌ فشل في تثبيت {req}")
            return False
    return True

def create_executable():
    """إنشاء الملف التنفيذي"""
    print("🚀 بدء إنشاء الملف التنفيذي...")
    
    # مسارات المشروع
    base_dir = Path(__file__).parent
    src_dir = base_dir / 'src'
    dist_dir = base_dir / 'dist'
    build_dir = base_dir / 'build'
    
    # تنظيف المجلدات السابقة
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    if build_dir.exists():
        shutil.rmtree(build_dir)
    
    # إنشاء ملف spec مخصص
    spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{src_dir / "__main__.py"}'],
    pathex=['{base_dir}'],
    binaries=[],
    datas=[
        ('{src_dir / "resources"}', 'resources'),
        ('{base_dir / "docs"}', 'docs'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'src.ui.welcome',
        'src.ui.data_entry',
        'src.ui.report',
        'src.ui.archive',
        'src.ui.filter',
        'src.ui.settings',
        'src.database.db_manager',
        'src.utils.themes',
        'src.utils.sound',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='برنامج_الأرشفة_الإلكترونية',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    # كتابة ملف spec
    spec_file = base_dir / 'app.spec'
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    # تشغيل PyInstaller
    try:
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            str(spec_file)
        ]
        
        print(f"🔨 تشغيل الأمر: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=base_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            exe_path = dist_dir / 'برنامج_الأرشفة_الإلكترونية.exe'
            if exe_path.exists():
                print(f"✅ تم إنشاء الملف التنفيذي بنجاح!")
                print(f"📁 المسار: {exe_path}")
                print(f"📊 حجم الملف: {exe_path.stat().st_size / (1024*1024):.1f} MB")
                return True
            else:
                print("❌ لم يتم العثور على الملف التنفيذي")
                return False
        else:
            print(f"❌ خطأ في إنشاء الملف التنفيذي:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏗️  مولد الملف التنفيذي - برنامج الأرشفة الإلكترونية")
    print("=" * 50)
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return
    
    # إنشاء الملف التنفيذي
    if create_executable():
        print("\n🎉 تم إنشاء التطبيق بنجاح!")
        print("💡 يمكنك الآن تشغيل البرنامج من مجلد dist")
    else:
        print("\n❌ فشل في إنشاء التطبيق")

if __name__ == "__main__":
    main()
