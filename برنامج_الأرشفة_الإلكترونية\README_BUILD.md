# 🏗️ دليل تحويل البرنامج إلى تطبيق سطح مكتب

## 📋 المتطلبات الأساسية

### 1. تثبيت Python
- تأكد من تثبيت Python 3.8 أو أحدث
- تحقق من التثبيت: `python --version`

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

## 🚀 طرق إنشاء التطبيق

### الطريقة الأولى: استخدام السكريبت الآلي (الأسهل)

#### على Windows:
1. انقر نقراً مزدوجاً على `build_app.bat`
2. انتظر حتى انتهاء العملية
3. ستجد الملف التنفيذي في مجلد `dist`

#### على Linux/Mac:
```bash
python create_exe.py
```

### الطريقة الثانية: استخدام PyInstaller يدوياً

#### 1. تثبيت PyInstaller
```bash
pip install pyinstaller
```

#### 2. إنشاء ملف تنفيذي واحد
```bash
pyinstaller --onefile --windowed --name="برنامج_الأرشفة_الإلكترونية" src/__main__.py
```

#### 3. إنشاء ملف تنفيذي مع الملفات المساعدة
```bash
pyinstaller --onedir --windowed --name="برنامج_الأرشفة_الإلكترونية" src/__main__.py
```

#### 4. مع إضافة الموارد والبيانات
```bash
pyinstaller --onefile --windowed \
  --add-data "src/resources;resources" \
  --add-data "docs;docs" \
  --name="برنامج_الأرشفة_الإلكترونية" \
  src/__main__.py
```

## 📁 هيكل المخرجات

```
dist/
├── برنامج_الأرشفة_الإلكترونية.exe  # الملف التنفيذي الرئيسي
└── _internal/                        # الملفات المساعدة (في حالة --onedir)
    ├── resources/                    # ملفات الموارد
    ├── docs/                        # ملفات التوثيق
    └── ...                          # مكتبات Python
```

## ⚙️ خيارات متقدمة

### إضافة أيقونة مخصصة
```bash
pyinstaller --onefile --windowed --icon="icon.ico" src/__main__.py
```

### تحسين الحجم
```bash
pyinstaller --onefile --windowed --optimize=2 --strip src/__main__.py
```

### إخفاء وحدة التحكم
```bash
pyinstaller --onefile --noconsole src/__main__.py
```

## 🔧 حل المشاكل الشائعة

### 1. خطأ "Module not found"
- تأكد من تثبيت جميع المتطلبات
- أضف الوحدات المفقودة باستخدام `--hidden-import`

### 2. حجم الملف كبير
- استخدم `--onedir` بدلاً من `--onefile`
- استخدم `--exclude-module` لاستبعاد الوحدات غير المطلوبة

### 3. البرنامج لا يعمل على أجهزة أخرى
- تأكد من تضمين جميع الملفات المطلوبة
- استخدم `--add-data` لإضافة الملفات الناقصة

## 📦 إنشاء مثبت (Installer)

### استخدام Inno Setup (Windows)
1. حمل Inno Setup من الموقع الرسمي
2. أنشئ سكريبت تثبيت
3. اكمبايل السكريبت لإنشاء مثبت .exe

### استخدام NSIS (Windows)
```nsis
!define APP_NAME "برنامج الأرشفة الإلكترونية"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "المحاسب المبرمج علي عاجل خشان المحنة"

OutFile "setup_archiving_program.exe"
InstallDir "$PROGRAMFILES\${APP_NAME}"
```

## 🎯 نصائح للحصول على أفضل النتائج

1. **اختبر على أجهزة مختلفة** قبل التوزيع
2. **أضف ملف README** مع التطبيق
3. **استخدم أيقونة مميزة** للتطبيق
4. **اضغط الملف النهائي** لتوفير مساحة التحميل
5. **أنشئ نسخة محمولة** لا تحتاج تثبيت

## 📞 الدعم الفني

في حالة مواجهة مشاكل:
1. تحقق من ملف `build.log`
2. راجع رسائل الخطأ في وحدة التحكم
3. تأكد من صحة مسارات الملفات
4. جرب إعادة بناء التطبيق من جديد
