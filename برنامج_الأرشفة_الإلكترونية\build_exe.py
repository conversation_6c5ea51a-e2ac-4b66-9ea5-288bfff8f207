import os
import sys
import PyInstaller.__main__

def create_executable():
    """إنشاء ملف تنفيذي للبرنامج"""
    # تحديد مسار المشروع
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # تحديد مسار الملف الرئيسي
    main_file = os.path.join(base_dir, 'src', '__init__.py')
    
    # تحديد مسار المخرجات
    dist_dir = os.path.join(base_dir, 'dist')
    
    # تحديد اسم الملف التنفيذي
    exe_name = 'برنامج_الأرشفة_الإلكترونية'
    
    # تحديد مسار الأيقونة
    icon_path = os.path.join(base_dir, 'src', 'resources', 'icons', 'app_icon.ico')
    
    # إنشاء أيقونة افتراضية إذا لم تكن موجودة
    if not os.path.exists(icon_path):
        os.makedirs(os.path.dirname(icon_path), exist_ok=True)
        # إنشاء ملف أيقونة فارغ
        with open(icon_path, 'wb') as f:
            f.write(b'')
    
    # تحديد خيارات PyInstaller
    options = [
        main_file,
        '--name=%s' % exe_name,
        '--onefile',
        '--windowed',
        '--icon=%s' % icon_path,
        '--add-data=%s;%s' % (os.path.join(base_dir, 'src', 'resources'), 'resources'),
        '--add-data=%s;%s' % (os.path.join(base_dir, 'docs'), 'docs'),
        '--distpath=%s' % dist_dir,
        '--clean',
    ]
    
    # تنفيذ PyInstaller
    PyInstaller.__main__.run(options)
    
    print(f"تم إنشاء الملف التنفيذي بنجاح في: {dist_dir}")
    return os.path.join(dist_dir, exe_name + '.exe')

if __name__ == "__main__":
    create_executable()
