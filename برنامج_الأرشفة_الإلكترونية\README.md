# 📁 برنامج الأرشفة الإلكترونية

## 📋 نظرة عامة
نظام متكامل لإدارة وأرشفة المستندات والمراسلات الرسمية، مطور بلغة Python باستخدام PyQt5.

**المطور**: المحاسب المبرمج علي عاجل خشان المحنة  
**الإصدار**: 1.0.0  
**التاريخ**: 2025

## ✨ المميزات

### 🏠 الواجهة الرئيسية
- واجهة ترحيب أنيقة مع تأثيرات بصرية
- أزرار تنقل كبيرة وواضحة
- خطوط محسنة للقراءة السهلة

### 📝 إدخال البيانات
- نماذج سهلة الاستخدام
- حفظ المستندات والمراسلات
- تصنيف حسب الدوائر والموظفين

### 🗃️ الأرشيف
- عرض جميع المستندات المحفوظة
- بحث متقدم في البيانات
- تصفية حسب معايير متعددة

### 📊 التقارير
- إنتاج تقارير مفصلة
- إحصائيات شاملة
- تصدير البيانات

### ⚙️ الإعدادات
- تخصيص المظهر والألوان
- إدارة قاعدة البيانات
- إعدادات النظام

## 🚀 طرق التشغيل

### الطريقة الأولى: تشغيل مباشر
```
1. انقر نقراً مزدوجاً على: install_requirements.bat
2. انتظر انتهاء تثبيت المتطلبات
3. انقر نقراً مزدوجاً على: run_app.bat
```

### الطريقة الثانية: إنشاء ملف تنفيذي
```
1. انقر نقراً مزدوجاً على: quick_build.bat
2. انتظر انتهاء عملية البناء
3. افتح مجلد dist وشغل الملف التنفيذي
```

### الطريقة الثالثة: من سطر الأوامر
```cmd
# تثبيت المتطلبات
pip install PyQt5 pyinstaller

# تشغيل البرنامج
python -m src

# أو إنشاء ملف تنفيذي
pyinstaller --onefile --windowed src/__main__.py
```

## 📁 هيكل المشروع

```
برنامج_الأرشفة_الإلكترونية/
├── src/                          # الكود المصدري
│   ├── __init__.py              # ملف التهيئة الرئيسي
│   ├── __main__.py              # نقطة الدخول
│   ├── main.py                  # التطبيق الرئيسي
│   ├── ui/                      # واجهات المستخدم
│   │   ├── welcome.py           # واجهة الترحيب
│   │   ├── data_entry.py        # واجهة إدخال البيانات
│   │   ├── archive.py           # واجهة الأرشيف
│   │   ├── report.py            # واجهة التقارير
│   │   ├── filter.py            # واجهة التصفية
│   │   └── settings.py          # واجهة الإعدادات
│   ├── database/                # قاعدة البيانات
│   │   └── db_manager.py        # مدير قاعدة البيانات
│   ├── utils/                   # الأدوات المساعدة
│   │   ├── themes.py            # إدارة السمات
│   │   ├── sound.py             # إدارة الأصوات
│   │   └── resources.py         # إدارة الموارد
│   └── resources/               # الملفات المساعدة
├── docs/                        # التوثيق
├── data/                        # قاعدة البيانات (تُنشأ تلقائياً)
├── dist/                        # الملفات التنفيذية (بعد البناء)
├── requirements.txt             # متطلبات Python
├── run_app.bat                  # تشغيل البرنامج
├── quick_build.bat              # إنشاء ملف تنفيذي سريع
├── install_requirements.bat     # تثبيت المتطلبات
└── README.md                    # هذا الملف
```

## 🔧 المتطلبات التقنية

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 512 MB RAM كحد أدنى
- **المساحة**: 100 MB مساحة فارغة

### مكتبات Python المطلوبة
- PyQt5 >= 5.15.0
- PyInstaller >= 4.0 (لإنشاء الملف التنفيذي)
- Pillow >= 8.0.0 (لمعالجة الصور)

## 🎯 الاستخدام

### البدء السريع
1. **تثبيت المتطلبات**: شغل `install_requirements.bat`
2. **تشغيل البرنامج**: شغل `run_app.bat`
3. **استكشاف الواجهات**: انقر على الأزرار في الواجهة الرئيسية

### إدخال البيانات
1. انقر على "إدخال بيانات جديدة"
2. املأ النموذج بالمعلومات المطلوبة
3. انقر "حفظ" لحفظ البيانات

### البحث والتصفية
1. انقر على "استعراض الأرشيف"
2. استخدم مربع البحث للعثور على مستندات محددة
3. استخدم "تصفية البيانات" للبحث المتقدم

## 🏗️ إنشاء ملف تنفيذي

### الطريقة السريعة
```
انقر نقراً مزدوجاً على: quick_build.bat
```

### الطريقة اليدوية
```cmd
pip install pyinstaller
pyinstaller --onefile --windowed --name="برنامج_الأرشفة" src/__main__.py
```

## 🔍 حل المشاكل

### مشكلة: البرنامج لا يعمل
**الحل**: تأكد من تثبيت Python و PyQt5
```cmd
python --version
pip install PyQt5
```

### مشكلة: خطأ في إنشاء الملف التنفيذي
**الحل**: تثبيت PyInstaller وإعادة المحاولة
```cmd
pip install pyinstaller
```

### مشكلة: الخطوط غير واضحة
**الحل**: تم تحسين الخطوط في الإصدار الحالي تلقائياً

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات:
- **المطور**: المحاسب المبرمج علي عاجل خشان المحنة
- **البريد الإلكتروني**: [يرجى إضافة البريد الإلكتروني]
- **الهاتف**: [يرجى إضافة رقم الهاتف]

## 📄 الترخيص

هذا البرنامج مطور لأغراض تعليمية وإدارية. جميع الحقوق محفوظة للمطور.

## 🙏 شكر وتقدير

شكر خاص لجميع من ساهم في تطوير وتحسين هذا البرنامج.

---

**🎉 مبروك! أصبح لديك نظام أرشفة إلكترونية متكامل!**
