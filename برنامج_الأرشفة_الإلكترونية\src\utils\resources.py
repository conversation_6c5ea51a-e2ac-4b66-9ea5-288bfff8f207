import os
import sys
import shutil
from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

class EventBus(QObject):
    """ناقل الأحداث للتواصل بين مكونات البرنامج"""
    
    # إشارات الأحداث
    record_added = pyqtSignal(int)  # إضافة سجل جديد (معرف السجل)
    record_updated = pyqtSignal(int)  # تحديث سجل (معرف السجل)
    record_deleted = pyqtSignal(int)  # حذف سجل (معرف السجل)
    settings_changed = pyqtSignal()  # تغيير الإعدادات
    theme_changed = pyqtSignal(str)  # تغيير السمة (اسم السمة)
    
    def __init__(self):
        super().__init__()

class ResourceManager:
    """مدير الموارد للتعامل مع الملفات والموارد الخارجية"""
    
    def __init__(self, base_dir):
        """تهيئة مدير الموارد"""
        self.base_dir = base_dir
        self.resources_dir = os.path.join(base_dir, 'src', 'resources')
        self.icons_dir = os.path.join(self.resources_dir, 'icons')
        self.sounds_dir = os.path.join(self.resources_dir, 'sounds')
        self.styles_dir = os.path.join(self.resources_dir, 'styles')
        
        # إنشاء المجلدات إذا لم تكن موجودة
        os.makedirs(self.icons_dir, exist_ok=True)
        os.makedirs(self.sounds_dir, exist_ok=True)
        os.makedirs(self.styles_dir, exist_ok=True)
        
        # إنشاء الموارد الافتراضية
        self.create_default_resources()
    
    def create_default_resources(self):
        """إنشاء الموارد الافتراضية"""
        # إنشاء ملفات الصوت الافتراضية
        self.create_default_sounds()
        
        # إنشاء ملفات الأنماط الافتراضية
        self.create_default_styles()
    
    def create_default_sounds(self):
        """إنشاء ملفات الصوت الافتراضية"""
        # في التطبيق الفعلي، يمكن نسخ ملفات الصوت من مجلد الموارد المضمنة
        # هنا نقوم بإنشاء ملفات فارغة للتجربة
        sound_files = ['click.wav', 'hover.wav', 'success.wav', 'error.wav', 'notification.wav']
        
        for sound_file in sound_files:
            sound_path = os.path.join(self.sounds_dir, sound_file)
            if not os.path.exists(sound_path):
                with open(sound_path, 'wb') as f:
                    # إنشاء ملف صوت WAV فارغ
                    f.write(b'RIFF\x24\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88\x58\x01\x00\x02\x00\x10\x00data\x00\x00\x00\x00')
    
    def create_default_styles(self):
        """إنشاء ملفات الأنماط الافتراضية"""
        # في التطبيق الفعلي، يمكن نسخ ملفات الأنماط من مجلد الموارد المضمنة
        # هنا نقوم بإنشاء ملفات CSS بسيطة للتجربة
        
        # النمط الافتراضي
        default_style = """
        /* النمط الافتراضي */
        QWidget {
            background-color: #f0f0f0;
            color: #333333;
            font-family: 'Arial', sans-serif;
        }
        
        QPushButton {
            background-color: #4a86e8;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #6fa8ff;
        }
        
        QPushButton:pressed {
            background-color: #2a66c8;
        }
        """
        
        # النمط الداكن
        dark_style = """
        /* النمط الداكن */
        QWidget {
            background-color: #303030;
            color: #ffffff;
            font-family: 'Arial', sans-serif;
        }
        
        QPushButton {
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #757de8;
        }
        
        QPushButton:pressed {
            background-color: #002984;
        }
        """
        
        # النمط الأنيق
        elegant_style = """
        /* النمط الأنيق */
        QWidget {
            background-color: #f5f5f5;
            color: #212121;
            font-family: 'Arial', sans-serif;
        }
        
        QPushButton {
            background-color: #212121;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #484848;
        }
        
        QPushButton:pressed {
            background-color: #000000;
        }
        """
        
        # كتابة ملفات الأنماط
        with open(os.path.join(self.styles_dir, 'default.css'), 'w', encoding='utf-8') as f:
            f.write(default_style)
        
        with open(os.path.join(self.styles_dir, 'dark.css'), 'w', encoding='utf-8') as f:
            f.write(dark_style)
        
        with open(os.path.join(self.styles_dir, 'elegant.css'), 'w', encoding='utf-8') as f:
            f.write(elegant_style)
    
    def get_icon_path(self, icon_name):
        """الحصول على مسار أيقونة"""
        return os.path.join(self.icons_dir, icon_name)
    
    def get_sound_path(self, sound_name):
        """الحصول على مسار ملف صوت"""
        return os.path.join(self.sounds_dir, sound_name)
    
    def get_style_path(self, style_name):
        """الحصول على مسار ملف نمط"""
        return os.path.join(self.styles_dir, style_name + '.css')
    
    def load_style(self, style_name):
        """تحميل ملف نمط"""
        style_path = self.get_style_path(style_name)
        if os.path.exists(style_path):
            with open(style_path, 'r', encoding='utf-8') as f:
                return f.read()
        return ""
