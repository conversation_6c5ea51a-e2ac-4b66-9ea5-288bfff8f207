import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QDateEdit, QTextEdit, QPushButton, QFormLayout,
                           QMessageBox, QGraphicsDropShadowEffect, QCalendarWidget,
                           QComboBox, QGroupBox, QCheckBox)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDate, QSize, QPropertyAnimation, QEasingCurve

from ..utils.themes import ThemeManager
from ..utils.sound import SoundManager
from ..database.db_manager import DatabaseManager

class FilterScreen(QMainWindow):
    """واجهة تصفية البيانات"""
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        self.sound_manager = SoundManager()
        self.db_manager = db_manager
        
        self.setWindowTitle("تصفية البيانات - برنامج الأرشفة الإلكترونية")
        self.setMinimumSize(800, 700)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد الخلفية والأنماط
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.theme_manager.get_color('background')};
            }}
            QLabel {{
                color: {self.theme_manager.get_color('text')};
                font-size: 14px;
            }}
            QLabel#header {{
                color: {self.theme_manager.get_color('primary')};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
            QGroupBox {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
                margin-top: 15px;
                font-weight: bold;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                color: {self.theme_manager.get_color('primary')};
            }}
            QLineEdit, QTextEdit, QDateEdit, QComboBox {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }}
            QLineEdit:focus, QTextEdit:focus, QDateEdit:focus, QComboBox:focus {{
                border: 2px solid {self.theme_manager.get_color('primary')};
            }}
            QPushButton {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme_manager.get_color('primary_light')};
            }}
            QPushButton#reset {{
                background-color: {self.theme_manager.get_color('warning')};
            }}
            QPushButton#reset:hover {{
                background-color: #ffb74d;
            }}
            QTableWidget {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                gridline-color: {self.theme_manager.get_color('primary_light')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
            }}
            QTableWidget::item:selected {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
            }}
            QHeaderView::section {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                padding: 5px;
                border: 1px solid {self.theme_manager.get_color('primary_dark')};
                font-weight: bold;
            }}
            QCheckBox {{
                color: {self.theme_manager.get_color('text')};
                font-size: 14px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {self.theme_manager.get_color('primary')};
                border: 2px solid {self.theme_manager.get_color('primary_dark')};
            }}
        """)
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        header_label = QLabel("تصفية البيانات")
        header_label.setObjectName("header")
        header_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header_label)
        
        # مجموعة تصفية حسب التاريخ
        date_group = QGroupBox("تصفية حسب التاريخ")
        date_layout = QVBoxLayout(date_group)
        
        # من تاريخ
        from_date_layout = QHBoxLayout()
        from_date_layout.setSpacing(10)
        
        from_date_label = QLabel("من تاريخ:")
        from_date_layout.addWidget(from_date_label)
        
        # اليوم
        self.from_day_input = QLineEdit()
        self.from_day_input.setPlaceholderText("اليوم")
        self.from_day_input.setMaximumWidth(80)
        self.add_shadow_effect(self.from_day_input)
        from_date_layout.addWidget(self.from_day_input)
        
        # الشهر
        self.from_month_input = QLineEdit()
        self.from_month_input.setPlaceholderText("الشهر")
        self.from_month_input.setMaximumWidth(80)
        self.add_shadow_effect(self.from_month_input)
        from_date_layout.addWidget(self.from_month_input)
        
        # السنة
        self.from_year_input = QLineEdit()
        self.from_year_input.setPlaceholderText("السنة")
        self.from_year_input.setMaximumWidth(100)
        self.add_shadow_effect(self.from_year_input)
        from_date_layout.addWidget(self.from_year_input)
        
        # زر التقويم
        from_calendar_button = QPushButton()
        from_calendar_button.setIcon(QIcon.fromTheme("office-calendar"))
        from_calendar_button.setIconSize(QSize(20, 20))
        from_calendar_button.setMaximumWidth(40)
        from_calendar_button.clicked.connect(lambda: self.show_calendar("from"))
        from_date_layout.addWidget(from_calendar_button)
        
        from_date_layout.addStretch()
        date_layout.addLayout(from_date_layout)
        
        # إلى تاريخ
        to_date_layout = QHBoxLayout()
        to_date_layout.setSpacing(10)
        
        to_date_label = QLabel("إلى تاريخ:")
        to_date_layout.addWidget(to_date_label)
        
        # اليوم
        self.to_day_input = QLineEdit()
        self.to_day_input.setPlaceholderText("اليوم")
        self.to_day_input.setMaximumWidth(80)
        self.add_shadow_effect(self.to_day_input)
        to_date_layout.addWidget(self.to_day_input)
        
        # الشهر
        self.to_month_input = QLineEdit()
        self.to_month_input.setPlaceholderText("الشهر")
        self.to_month_input.setMaximumWidth(80)
        self.add_shadow_effect(self.to_month_input)
        to_date_layout.addWidget(self.to_month_input)
        
        # السنة
        self.to_year_input = QLineEdit()
        self.to_year_input.setPlaceholderText("السنة")
        self.to_year_input.setMaximumWidth(100)
        self.add_shadow_effect(self.to_year_input)
        to_date_layout.addWidget(self.to_year_input)
        
        # زر التقويم
        to_calendar_button = QPushButton()
        to_calendar_button.setIcon(QIcon.fromTheme("office-calendar"))
        to_calendar_button.setIconSize(QSize(20, 20))
        to_calendar_button.setMaximumWidth(40)
        to_calendar_button.clicked.connect(lambda: self.show_calendar("to"))
        to_date_layout.addWidget(to_calendar_button)
        
        to_date_layout.addStretch()
        date_layout.addLayout(to_date_layout)
        
        main_layout.addWidget(date_group)
        
        # مجموعة تصفية حسب المحتوى
        content_group = QGroupBox("تصفية حسب المحتوى")
        content_layout = QFormLayout(content_group)
        content_layout.setLabelAlignment(Qt.AlignRight)
        content_layout.setFormAlignment(Qt.AlignRight)
        content_layout.setSpacing(15)
        
        # تصفية حسب الموضوع
        self.subject_input = QLineEdit()
        self.subject_input.setPlaceholderText("أدخل موضوع للتصفية")
        self.add_shadow_effect(self.subject_input)
        content_layout.addRow("الموضوع:", self.subject_input)
        
        # تصفية حسب اسم الموظف
        self.employee_input = QLineEdit()
        self.employee_input.setPlaceholderText("أدخل اسم الموظف للتصفية")
        self.add_shadow_effect(self.employee_input)
        content_layout.addRow("اسم الموظف:", self.employee_input)
        
        # تصفية حسب الدائرة المعنية
        self.department_combo = QComboBox()
        self.department_combo.setEditable(True)
        self.department_combo.setPlaceholderText("اختر أو أدخل اسم الدائرة")
        self.department_combo.addItems(["قسم الموارد البشرية", "قسم المحاسبة", "قسم تكنولوجيا المعلومات", "قسم الإدارة", "قسم المبيعات"])
        self.add_shadow_effect(self.department_combo)
        content_layout.addRow("الدائرة المعنية:", self.department_combo)
        
        main_layout.addWidget(content_group)
        
        # مجموعة تصفية حسب الهامش
        notes_group = QGroupBox("تصفية حسب الهامش")
        notes_layout = QVBoxLayout(notes_group)
        
        self.has_notes_checkbox = QCheckBox("تحتوي على هامش")
        self.has_notes_checkbox.setChecked(False)
        notes_layout.addWidget(self.has_notes_checkbox)
        
        self.no_notes_checkbox = QCheckBox("لا تحتوي على هامش")
        self.no_notes_checkbox.setChecked(False)
        notes_layout.addWidget(self.no_notes_checkbox)
        
        # ربط الخانات لضمان عدم اختيار الخيارين معاً
        self.has_notes_checkbox.stateChanged.connect(self.update_notes_checkboxes)
        self.no_notes_checkbox.stateChanged.connect(self.update_notes_checkboxes)
        
        main_layout.addWidget(notes_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر تطبيق التصفية
        self.apply_button = QPushButton("تطبيق التصفية")
        self.apply_button.setIcon(QIcon.fromTheme("view-filter"))
        self.apply_button.setIconSize(QSize(20, 20))
        self.apply_button.setMinimumWidth(150)
        self.apply_button.clicked.connect(self.apply_filter)
        self.add_shadow_effect(self.apply_button)
        buttons_layout.addWidget(self.apply_button)
        
        # زر إعادة تعيين
        self.reset_button = QPushButton("إعادة تعيين")
        self.reset_button.setObjectName("reset")
        self.reset_button.setIcon(QIcon.fromTheme("edit-clear"))
        self.reset_button.setIconSize(QSize(20, 20))
        self.reset_button.setMinimumWidth(150)
        self.reset_button.clicked.connect(self.reset_filter)
        self.add_shadow_effect(self.reset_button)
        buttons_layout.addWidget(self.reset_button)
        
        # زر العودة للقائمة الرئيسية
        self.back_button = QPushButton("العودة للقائمة الرئيسية")
        self.back_button.setIcon(QIcon.fromTheme("go-home"))
        self.back_button.setIconSize(QSize(20, 20))
        self.back_button.setMinimumWidth(150)
        self.back_button.clicked.connect(self.go_back)
        self.add_shadow_effect(self.back_button)
        buttons_layout.addWidget(self.back_button)
        
        main_layout.addLayout(buttons_layout)
    
    def add_shadow_effect(self, widget):
        """إضافة تأثير الظل للعناصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 3)
        widget.setGraphicsEffect(shadow)
    
    def show_calendar(self, date_type):
        """عرض التقويم لاختيار التاريخ"""
        self.sound_manager.play_click_sound()
        
        # إنشاء نافذة التقويم
        self.calendar_dialog = QWidget(self)
        self.calendar_dialog.setWindowFlags(Qt.Popup)
        self.calendar_dialog.setStyleSheet(f"""
            QWidget {{
                background-color: {self.theme_manager.get_color('card')};
                border: 1px solid {self.theme_manager.get_color('primary')};
                border-radius: 5px;
            }}
            QCalendarWidget {{
                background-color: {self.theme_manager.get_color('card')};
            }}
            QCalendarWidget QToolButton {{
                color: {self.theme_manager.get_color('text')};
                background-color: {self.theme_manager.get_color('primary_light')};
                border-radius: 3px;
            }}
            QCalendarWidget QMenu {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
            }}
            QCalendarWidget QSpinBox {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
            }}
            QCalendarWidget QAbstractItemView:enabled {{
                color: {self.theme_manager.get_color('text')};
                background-color: {self.theme_manager.get_color('card')};
                selection-background-color: {self.theme_manager.get_color('primary')};
                selection-color: white;
            }}
        """)
        
        layout = QVBoxLayout(self.calendar_dialog)
        
        calendar = QCalendarWidget()
        calendar.setGridVisible(True)
        
        if date_type == "from":
            calendar.clicked.connect(self.set_from_date)
            pos = self.from_year_input.mapToGlobal(self.from_year_input.rect().bottomLeft())
        else:
            calendar.clicked.connect(self.set_to_date)
            pos = self.to_year_input.mapToGlobal(self.to_year_input.rect().bottomLeft())
        
        layout.addWidget(calendar)
        
        # تحديد موقع التقويم
        self.calendar_dialog.move(pos)
        self.calendar_dialog.show()
    
    def set_from_date(self, date):
        """تعيين تاريخ البداية من التقويم"""
        self.from_day_input.setText(str(date.day()))
        self.from_month_input.setText(str(date.month()))
        self.from_year_input.setText(str(date.year()))
        self.calendar_dialog.close()
    
    def set_to_date(self, date):
        """تعيين تاريخ النهاية من التقويم"""
        self.to_day_input.setText(str(date.day()))
        self.to_month_input.setText(str(date.month()))
        self.to_year_input.setText(str(date.year()))
        self.calendar_dialog.close()
    
    def update_notes_checkboxes(self, state):
        """تحديث حالة خانات اختيار الهامش"""
        sender = self.sender()
        
        if sender == self.has_notes_checkbox and state == Qt.Checked:
            self.no_notes_checkbox.setChecked(False)
        elif sender == self.no_notes_checkbox and state == Qt.Checked:
            self.has_notes_checkbox.setChecked(False)
    
    def apply_filter(self):
        """تطبيق التصفية"""
        self.sound_manager.play_click_sound()
        
        # استخراج معايير التصفية
        from_date = None
        if self.from_day_input.text() and self.from_month_input.text() and self.from_year_input.text():
            try:
                from_day = int(self.from_day_input.text())
                from_month = int(self.from_month_input.text())
                from_year = int(self.from_year_input.text())
                from_date = (from_day, from_month, from_year)
            except ValueError:
                self.sound_manager.play_error_sound()
                QMessageBox.warning(self, "تنبيه", "يرجى إدخال تاريخ بداية صالح!")
                return
        
        to_date = None
        if self.to_day_input.text() and self.to_month_input.text() and self.to_year_input.text():
            try:
                to_day = int(self.to_day_input.text())
                to_month = int(self.to_month_input.text())
                to_year = int(self.to_year_input.text())
                to_date = (to_day, to_month, to_year)
            except ValueError:
                self.sound_manager.play_error_sound()
                QMessageBox.warning(self, "تنبيه", "يرجى إدخال تاريخ نهاية صالح!")
                return
        
        subject = self.subject_input.text() if self.subject_input.text() else None
        employee = self.employee_input.text() if self.employee_input.text() else None
        department = self.department_combo.currentText() if self.department_combo.currentText() else None
        
        has_notes = None
        if self.has_notes_checkbox.isChecked():
            has_notes = True
        elif self.no_notes_checkbox.isChecked():
            has_notes = False
        
        # تطبيق التصفية
        if self.db_manager:
            filtered_records = self.db_manager.filter_records(
                start_date=from_date,
                end_date=to_date,
                subject=subject,
                employee=employee,
                department=department,
                has_notes=has_notes
            )
            
            if filtered_records:
                self.sound_manager.play_success_sound()
                QMessageBox.information(self, "نتائج التصفية", f"تم العثور على {len(filtered_records)} سجل مطابق.")
                # هنا يمكن فتح نافذة لعرض النتائج
            else:
                self.sound_manager.play_notification_sound()
                QMessageBox.information(self, "نتائج التصفية", "لم يتم العثور على سجلات مطابقة.")
        else:
            # للاختبار فقط عندما لا تكون قاعدة البيانات متصلة
            self.sound_manager.play_success_sound()
            QMessageBox.information(self, "نتائج التصفية (وضع الاختبار)", "تم تطبيق التصفية بنجاح!")
    
    def reset_filter(self):
        """إعادة تعيين معايير التصفية"""
        self.sound_manager.play_click_sound()
        
        # مسح جميع الحقول
        self.from_day_input.clear()
        self.from_month_input.clear()
        self.from_year_input.clear()
        self.to_day_input.clear()
        self.to_month_input.clear()
        self.to_year_input.clear()
        self.subject_input.clear()
        self.employee_input.clear()
        self.department_combo.setCurrentIndex(-1)
        self.has_notes_checkbox.setChecked(False)
        self.no_notes_checkbox.setChecked(False)
    
    def go_back(self):
        """العودة إلى القائمة الرئيسية"""
        self.sound_manager.play_click_sound()
        self.close()
