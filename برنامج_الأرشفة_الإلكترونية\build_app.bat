@echo off
chcp 65001 >nul
echo ================================================
echo 🏗️  بناء تطبيق الأرشفة الإلكترونية
echo ================================================
echo.

echo 🔧 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت PyInstaller...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت PyInstaller
    pause
    exit /b 1
)

echo.
echo 🚀 إنشاء الملف التنفيذي...
pyinstaller --onefile --windowed --name="برنامج_الأرشفة_الإلكترونية" --add-data "src/resources;resources" --add-data "docs;docs" src/__main__.py

echo.
echo ✅ انتهت عملية البناء
echo 📁 تحقق من مجلد dist للملف التنفيذي
echo.
pause
