import os
from PyQt5.QtCore import Qt, QSettings

class ThemeManager:
    """مدير السمات والألوان في البرنامج"""
    
    def __init__(self):
        """تهيئة مدير السمات"""
        self.settings = QSettings("ElectronicArchiving", "AppSettings")
        self.load_theme()
    
    def load_theme(self):
        """تحميل السمة الحالية من الإعدادات"""
        self.theme_name = self.settings.value("theme/name", "default")
        
        # الألوان الافتراضية
        self.colors = {
            'default': {
                'primary': '#4a86e8',
                'primary_light': '#6fa8ff',
                'primary_dark': '#2a66c8',
                'accent': '#ff7043',
                'accent_light': '#ff9a76',
                'accent_dark': '#c63f17',
                'background': '#f5f5f5',
                'card': '#ffffff',
                'text': '#333333',
                'text_light': '#757575',
                'success': '#4caf50',
                'warning': '#ff9800',
                'error': '#f44336'
            },
            'dark': {
                'primary': '#3f51b5',
                'primary_light': '#757de8',
                'primary_dark': '#002984',
                'accent': '#ff4081',
                'accent_light': '#ff79b0',
                'accent_dark': '#c60055',
                'background': '#303030',
                'card': '#424242',
                'text': '#ffffff',
                'text_light': '#bdbdbd',
                'success': '#4caf50',
                'warning': '#ff9800',
                'error': '#f44336'
            },
            'elegant': {
                'primary': '#212121',
                'primary_light': '#484848',
                'primary_dark': '#000000',
                'accent': '#ffc107',
                'accent_light': '#fff350',
                'accent_dark': '#c79100',
                'background': '#f5f5f5',
                'card': '#ffffff',
                'text': '#212121',
                'text_light': '#757575',
                'success': '#4caf50',
                'warning': '#ff9800',
                'error': '#f44336'
            }
        }
        
        # تحميل الألوان المخصصة إن وجدت
        if self.theme_name not in self.colors:
            self.theme_name = "default"
    
    def get_color(self, color_name):
        """الحصول على لون محدد من السمة الحالية"""
        if self.theme_name in self.colors and color_name in self.colors[self.theme_name]:
            return self.colors[self.theme_name][color_name]
        return self.colors['default'][color_name]
    
    def set_theme(self, theme_name):
        """تغيير السمة الحالية"""
        if theme_name in self.colors:
            self.theme_name = theme_name
            self.settings.setValue("theme/name", theme_name)
            return True
        return False
    
    def get_available_themes(self):
        """الحصول على قائمة السمات المتاحة"""
        return list(self.colors.keys())
    
    def get_stylesheet(self):
        """الحصول على ورقة الأنماط الكاملة للسمة الحالية"""
        theme = self.colors[self.theme_name]
        
        return f"""
            /* الأنماط العامة */
            QWidget {{
                background-color: {theme['background']};
                color: {theme['text']};
                font-family: 'Arial', sans-serif;
            }}
            
            /* الأزرار */
            QPushButton {{
                background-color: {theme['primary']};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: {theme['primary_light']};
            }}
            
            QPushButton:pressed {{
                background-color: {theme['primary_dark']};
            }}
            
            /* حقول الإدخال */
            QLineEdit, QTextEdit, QComboBox {{
                background-color: {theme['card']};
                color: {theme['text']};
                border: 1px solid {theme['primary_light']};
                border-radius: 4px;
                padding: 6px;
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border: 2px solid {theme['primary']};
            }}
            
            /* الجداول */
            QTableView {{
                background-color: {theme['card']};
                color: {theme['text']};
                gridline-color: {theme['primary_light']};
                border: 1px solid {theme['primary_light']};
                border-radius: 4px;
            }}
            
            QTableView::item:selected {{
                background-color: {theme['primary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background-color: {theme['primary']};
                color: white;
                padding: 4px;
                border: 1px solid {theme['primary_dark']};
            }}
            
            /* القوائم */
            QMenu {{
                background-color: {theme['card']};
                color: {theme['text']};
                border: 1px solid {theme['primary_light']};
            }}
            
            QMenu::item:selected {{
                background-color: {theme['primary']};
                color: white;
            }}
            
            /* شريط التمرير */
            QScrollBar:vertical {{
                background-color: {theme['background']};
                width: 12px;
                margin: 0px;
            }}
            
            QScrollBar::handle:vertical {{
                background-color: {theme['primary']};
                min-height: 20px;
                border-radius: 6px;
            }}
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
        """
