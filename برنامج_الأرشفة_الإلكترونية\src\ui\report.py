import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QTableWidget, QTableWidgetItem, QPushButton, QHeaderView,
                           QMessageBox, QGraphicsDropShadowEffect, QAbstractItemView)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve, QDate
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

from ..utils.themes import ThemeManager
from ..utils.sound import SoundManager
from ..database.db_manager import DatabaseManager

class ReportScreen(QMainWindow):
    """واجهة عرض التقارير"""
    
    def __init__(self, parent=None, db_manager=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        self.sound_manager = SoundManager()
        self.db_manager = db_manager
        
        self.setWindowTitle("عرض التقارير - برنامج الأرشفة الإلكترونية")
        self.setMinimumSize(900, 700)
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد الخلفية والأنماط
        self.setStyleSheet(f"""
            QMainWindow {{
                background: {self.theme_manager.get_color('background')};
            }}
            QLabel {{
                color: {self.theme_manager.get_color('text')};
                font-size: 14px;
            }}
            QLabel#header {{
                color: {self.theme_manager.get_color('primary')};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
            QTableWidget {{
                background-color: {self.theme_manager.get_color('card')};
                color: {self.theme_manager.get_color('text')};
                gridline-color: {self.theme_manager.get_color('primary_light')};
                border: 1px solid {self.theme_manager.get_color('primary_light')};
                border-radius: 5px;
            }}
            QTableWidget::item:selected {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
            }}
            QHeaderView::section {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                padding: 5px;
                border: 1px solid {self.theme_manager.get_color('primary_dark')};
                font-weight: bold;
            }}
            QPushButton {{
                background-color: {self.theme_manager.get_color('primary')};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme_manager.get_color('primary_light')};
            }}
            QPushButton#print {{
                background-color: {self.theme_manager.get_color('success')};
            }}
            QPushButton#print:hover {{
                background-color: #66bb6a;
            }}
            QPushButton#export {{
                background-color: {self.theme_manager.get_color('accent')};
            }}
            QPushButton#export:hover {{
                background-color: {self.theme_manager.get_color('accent_light')};
            }}
        """)
        
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        header_label = QLabel("تقرير السجلات")
        header_label.setObjectName("header")
        header_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(header_label)
        
        # جدول البيانات
        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            "رقم الكتاب", "تاريخ الكتاب", "الموضوع", 
            "الفحوى/الموظف", "الدائرة المعنية", "الهامش",
            "تاريخ الإنشاء", "آخر تعديل"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.verticalHeader().setVisible(True)
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.add_shadow_effect(self.table)
        main_layout.addWidget(self.table)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر الطباعة
        self.print_button = QPushButton("طباعة التقرير")
        self.print_button.setObjectName("print")
        self.print_button.setIcon(QIcon.fromTheme("document-print"))
        self.print_button.setIconSize(QSize(20, 20))
        self.print_button.setMinimumWidth(150)
        self.print_button.clicked.connect(self.print_report)
        self.add_shadow_effect(self.print_button)
        buttons_layout.addWidget(self.print_button)
        
        # زر معاينة الطباعة
        self.preview_button = QPushButton("معاينة الطباعة")
        self.preview_button.setIcon(QIcon.fromTheme("document-print-preview"))
        self.preview_button.setIconSize(QSize(20, 20))
        self.preview_button.setMinimumWidth(150)
        self.preview_button.clicked.connect(self.preview_report)
        self.add_shadow_effect(self.preview_button)
        buttons_layout.addWidget(self.preview_button)
        
        # زر تصدير البيانات
        self.export_button = QPushButton("تصدير البيانات")
        self.export_button.setObjectName("export")
        self.export_button.setIcon(QIcon.fromTheme("document-save"))
        self.export_button.setIconSize(QSize(20, 20))
        self.export_button.setMinimumWidth(150)
        self.export_button.clicked.connect(self.export_data)
        self.add_shadow_effect(self.export_button)
        buttons_layout.addWidget(self.export_button)
        
        # زر العودة للقائمة الرئيسية
        self.back_button = QPushButton("العودة للقائمة الرئيسية")
        self.back_button.setIcon(QIcon.fromTheme("go-home"))
        self.back_button.setIconSize(QSize(20, 20))
        self.back_button.setMinimumWidth(150)
        self.back_button.clicked.connect(self.go_back)
        self.add_shadow_effect(self.back_button)
        buttons_layout.addWidget(self.back_button)
        
        main_layout.addLayout(buttons_layout)
    
    def add_shadow_effect(self, widget):
        """إضافة تأثير الظل للعناصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 3)
        widget.setGraphicsEffect(shadow)
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        if self.db_manager:
            records = self.db_manager.get_all_records()
        else:
            # بيانات تجريبية للاختبار
            records = [
                {
                    'id': 1,
                    'document_number': '123/2025',
                    'document_day': 15,
                    'document_month': 5,
                    'document_year': 2025,
                    'subject': 'طلب موافقة',
                    'content_or_employee': 'محمد أحمد',
                    'department': 'قسم الموارد البشرية',
                    'notes': 'تمت الموافقة',
                    'creation_date': '2025-05-15 10:30:00',
                    'last_modified': '2025-05-15 10:30:00'
                },
                {
                    'id': 2,
                    'document_number': '124/2025',
                    'document_day': 16,
                    'document_month': 5,
                    'document_year': 2025,
                    'subject': 'طلب إجازة',
                    'content_or_employee': 'علي محمود',
                    'department': 'قسم المحاسبة',
                    'notes': 'قيد المراجعة',
                    'creation_date': '2025-05-16 11:45:00',
                    'last_modified': '2025-05-16 11:45:00'
                }
            ]
        
        # تعيين عدد الصفوف
        self.table.setRowCount(len(records))
        
        # ملء الجدول بالبيانات
        for row, record in enumerate(records):
            # رقم الكتاب
            self.table.setItem(row, 0, QTableWidgetItem(record['document_number']))
            
            # تاريخ الكتاب
            date_str = f"{record['document_day']}/{record['document_month']}/{record['document_year']}"
            self.table.setItem(row, 1, QTableWidgetItem(date_str))
            
            # الموضوع
            self.table.setItem(row, 2, QTableWidgetItem(record['subject']))
            
            # الفحوى/الموظف
            self.table.setItem(row, 3, QTableWidgetItem(record['content_or_employee']))
            
            # الدائرة المعنية
            self.table.setItem(row, 4, QTableWidgetItem(record['department']))
            
            # الهامش
            self.table.setItem(row, 5, QTableWidgetItem(record['notes']))
            
            # تاريخ الإنشاء
            self.table.setItem(row, 6, QTableWidgetItem(record['creation_date']))
            
            # آخر تعديل
            self.table.setItem(row, 7, QTableWidgetItem(record['last_modified']))
    
    def print_report(self):
        """طباعة التقرير"""
        self.sound_manager.play_click_sound()
        
        printer = QPrinter(QPrinter.HighResolution)
        dialog = QPrintDialog(printer, self)
        
        if dialog.exec_() == QPrintDialog.Accepted:
            self.print_table(printer)
    
    def preview_report(self):
        """معاينة التقرير قبل الطباعة"""
        self.sound_manager.play_click_sound()
        
        printer = QPrinter(QPrinter.HighResolution)
        preview = QPrintPreviewDialog(printer, self)
        preview.paintRequested.connect(self.print_table)
        preview.exec_()
    
    def print_table(self, printer):
        """طباعة محتوى الجدول"""
        # هذه الوظيفة تحتاج إلى تنفيذ كامل لطباعة الجدول
        # في التطبيق الفعلي، يمكن استخدام QPainter لرسم الجدول على الطابعة
        pass
    
    def export_data(self):
        """تصدير البيانات إلى ملف"""
        self.sound_manager.play_click_sound()
        
        # في التطبيق الفعلي، يمكن إضافة خيارات لتصدير البيانات بتنسيقات مختلفة
        # مثل CSV أو Excel أو PDF
        QMessageBox.information(self, "تصدير البيانات", "سيتم تنفيذ هذه الميزة في الإصدار القادم.")
    
    def go_back(self):
        """العودة إلى القائمة الرئيسية"""
        self.sound_manager.play_click_sound()
        self.close()
