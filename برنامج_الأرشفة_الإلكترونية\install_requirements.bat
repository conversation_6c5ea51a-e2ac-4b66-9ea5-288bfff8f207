@echo off
chcp 65001 >nul
echo ================================================
echo 📦 تثبيت متطلبات برنامج الأرشفة الإلكترونية
echo ================================================
echo.

echo 🔧 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تحميل وتثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت المتطلبات الأساسية...

echo تثبيت PyQt5...
pip install PyQt5>=5.15.0
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت PyQt5
    pause
    exit /b 1
)

echo تثبيت Pillow...
pip install Pillow>=8.0.0
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: فشل في تثبيت Pillow
)

echo تثبيت PyInstaller...
pip install PyInstaller>=4.0
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: فشل في تثبيت PyInstaller
)

echo.
echo ✅ تم تثبيت المتطلبات بنجاح!
echo 🚀 يمكنك الآن تشغيل البرنامج باستخدام run_app.bat
echo 🏗️ أو إنشاء ملف تنفيذي باستخدام quick_build.bat
echo.
pause
