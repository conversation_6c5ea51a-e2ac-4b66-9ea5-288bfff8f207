
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['C:\Users\<USER>\Desktop\كوثر\برنامج_الأرشفة_الإلكترونية - Copy\برنامج_الأرشفة_الإلكترونية\src\__main__.py'],
    pathex=['C:\Users\<USER>\Desktop\كوثر\برنامج_الأرشفة_الإلكترونية - Copy\برنامج_الأرشفة_الإلكترونية'],
    binaries=[],
    datas=[
        ('C:\Users\<USER>\Desktop\كوثر\برنامج_الأرشفة_الإلكترونية - Copy\برنامج_الأرشفة_الإلكترونية\src\resources', 'resources'),
        ('C:\Users\<USER>\Desktop\كوثر\برنامج_الأرشفة_الإلكترونية - Copy\برنامج_الأرشفة_الإلكترونية\docs', 'docs'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'src.ui.welcome',
        'src.ui.data_entry',
        'src.ui.report',
        'src.ui.archive',
        'src.ui.filter',
        'src.ui.settings',
        'src.database.db_manager',
        'src.utils.themes',
        'src.utils.sound',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='برنامج_الأرشفة_الإلكترونية',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
