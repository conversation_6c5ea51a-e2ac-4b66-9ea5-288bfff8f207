import os
import sys
from PyQt5.QtWidgets import QApplication

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from src.main import MainApplication

def run():
    """تشغيل البرنامج"""
    app = MainApplication()
    exit_code = app.run()
    app.cleanup()
    sys.exit(exit_code)

if __name__ == "__main__":
    run()
